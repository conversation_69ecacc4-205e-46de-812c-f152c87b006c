# BeautyCall Monkey

Android UI 自动化测试项目，用于 BeautyCall 应用的自动化测试。

## 环境要求

- Python 3.8+
- Android 设备（已开启 USB 调试）
- ADB 工具

## 项目结构

```
monkey/
├── src/                # 源代码目录
│   ├── six/           # 主要测试模块
│   │   ├── six.py     # 主测试脚本
│   │   ├── six_auto.py # 自动化工具类
│   │   ├── home_monkey.py # 首页测试
│   │   └── room_monkey.py # 房间测试
│   └── mytest/        # 测试常量模块
├── myenv/             # Python 虚拟环境
└── pyproject.toml     # 项目配置文件
```

## 安装步骤

1. 创建并激活虚拟环境：
```bash
# 创建虚拟环境
python -m venv myenv

# 激活虚拟环境
# macOS/Linux
source myenv/bin/activate
# Windows
myenv\Scripts\activate
```

2. 安装项目依赖：
```bash
# 安装项目（开发模式）
pip install -e .
```

3. 初始化 uiautomator2：
```bash
# 确保 Android 设备已连接
python -m uiautomator2 init
```

## 运行测试

1. 确保 Android 设备已连接并开启 USB 调试：
```bash
# 检查设备连接状态
adb devices
```

2. 运行测试：
```bash
# 运行主测试程序
python -m src
```

## 常见问题

1. 设备连接问题
   - 确保设备已开启 USB 调试
   - 检查 USB 连接是否稳定
   - 运行 `adb devices` 确认设备已识别

2. 依赖安装问题
   - 确保在虚拟环境中运行
   - 检查 `pip install -e .` 是否成功
   - 确认所有依赖都已正确安装

3. uiautomator2 初始化问题
   - 确保设备已正确连接
   - 检查设备是否已授权 USB 调试
   - 尝试重新运行 `python -m uiautomator2 init`

## 开发说明

1. 添加新的测试模块：
   - 在 `src/six/` 目录下创建新的测试文件
   - 在 `pyproject.toml` 中更新包配置
   - 重新运行 `pip install -e .`

2. 修改测试常量：
   - 在 `src/mytest/` 目录下修改常量文件
   - 重新运行测试程序

## 注意事项

- 运行前请确保设备已正确连接
- 建议在虚拟环境中运行项目
- 保持 ADB 服务正常运行
- 定期检查设备连接状态 

## 参考文档
- https://github.com/openatx/uiautomator2?tab=readme-ov-file