# BeautyCall Monkey

Android UI 自动化测试项目，用于 BeautyCall 应用的自动化测试。

## 环境要求

- Python 3.8+
- Android 设备（已开启 USB 调试）
- ADB 工具

## 项目结构

```
monkey/
├── src/                    # 源代码目录
│   ├── __init__.py        # 主入口文件
│   ├── BaseMonkey.py      # 基础 Monkey 类
│   ├── monkey_config.py   # 配置类定义
│   ├── monkey_run.py      # 单设备运行逻辑
│   ├── monkey_auto_factory.py # Monkey 工厂类
│   ├── monkey_proxy_mixin.py  # Monkey 代理混入类
│   ├── multiprocess_monkey_run.py # 多进程多设备运行
│   ├── models/            # 数据模型
│   │   ├── monkey_context.py # Monkey 上下文
│   │   └── terminated_monkey_error.py # 终止异常类
│   ├── util/              # 工具类
│   │   ├── adb_util.py    # ADB 工具
│   │   ├── app_util.py    # 应用工具
│   │   ├── config_loader.py # 配置加载器
│   │   ├── file_util.py   # 文件工具
│   │   ├── keep_alive.py  # 防息屏保活
│   │   ├── local_data_collect.py # 本地数据收集
│   │   ├── logger.py      # 日志工具
│   │   ├── send_result_file.py # 结果文件发送
│   │   └── uiautomator_util.py # UI 自动化工具
│   ├── robot/             # 机器人通知模块
│   ├── bixin/             # 比心应用模块
│   ├── hezi/              # 赫兹应用模块
│   ├── huajiao/           # 花椒应用模块
│   ├── kugou/             # 酷狗应用模块
│   ├── kuaishou/          # 快手应用模块
│   ├── momo/              # 陌陌应用模块
│   ├── six/               # 六间房应用模块
│   ├── soul/              # Soul 应用模块
│   ├── soul2/             # Soul2 应用模块
│   ├── xiaolu/            # 小鹿应用模块
│   ├── xinyu/             # 心遇应用模块
│   ├── xiuse/             # 秀色应用模块
│   ├── yy/                # YY 应用模块
│   └── yomi/              # Yo语音应用模块
├── configs/               # 配置文件目录
│   ├── monkey_config.json # 主配置文件
│   ├── monkey_config_1.json # 配置文件1
│   └── monkey_config_with_publicly.json # 带公屏消息的配置
├── results/               # 测试结果目录
├── apk/                   # APK 文件目录
├── myenv/                 # Python 虚拟环境
├── pyproject.toml         # 项目配置文件
├── requirements.txt       # Python 依赖文件
└── README.md             # 项目说明文档
```

## 安装步骤

1. 创建并激活虚拟环境：
```bash
# 创建虚拟环境
python -m venv myenv

# 激活虚拟环境
# macOS/Linux
source myenv/bin/activate
# Windows
myenv\Scripts\activate
```

2. 安装项目依赖：
```bash
# 安装项目（开发模式）
pip install -e .
```

3. 初始化 uiautomator2：
```bash
# 确保 Android 设备已连接
python -m uiautomator2 init
```

## 运行测试

1. 确保 Android 设备已连接并开启 USB 调试：
```bash
# 检查设备连接状态
adb devices
```

2. 运行测试：
```bash
# 运行主测试程序
python -m src
```

## 配置说明

项目支持多种运行模式：
1. **直接运行**：立即执行自动化脚本
2. **定时任务**：按配置的时间自动执行
3. **数据统计**：收集历史运行数据
4. **结果发送**：发送测试结果到如流

配置文件位于 `configs/` 目录，支持：
- 应用配置：每个应用的测试参数
- 定时配置：每天的执行时间
- 消息配置：发送的文本内容

## 常见问题

1. 设备连接问题
   - 确保设备已开启 USB 调试
   - 检查 USB 连接是否稳定
   - 运行 `adb devices` 确认设备已识别

2. 依赖安装问题
   - 确保在虚拟环境中运行
   - 检查 `pip install -e .` 是否成功
   - 确认所有依赖都已正确安装

3. uiautomator2 初始化问题
   - 确保设备已正确连接
   - 检查设备是否已授权 USB 调试
   - 尝试重新运行 `python -m uiautomator2 init`

## 开发说明

1. 添加新的应用模块：
   - 在 `src/` 目录下创建新的应用目录
   - 继承 `BaseMonkey` 类实现具体逻辑
   - 在 `monkey_auto_factory.py` 中注册新模块
   - 在配置文件中添加应用配置

2. 修改配置：
   - 编辑 `configs/monkey_config.json` 文件
   - 支持热重载，无需重启程序

3. 查看结果：
   - 测试结果保存在 `results/` 目录
   - 支持按时间范围统计数据
   - 可发送结果到如流机器人

## 注意事项

- 运行前请确保设备已正确连接
- 建议在虚拟环境中运行项目
- 保持 ADB 服务正常运行
- 定期检查设备连接状态
- 配置文件使用 JSON 格式，支持 pydantic 验证

## 参考文档
- https://github.com/openatx/uiautomator2?tab=readme-ov-file