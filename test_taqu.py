#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
他趣自动化脚本测试文件
用于验证他趣自动化脚本是否能正确导入和初始化
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext
from monkey_auto_factory import MonkeyAutoFactory

def test_taqu_auto():
    """测试他趣自动化脚本的创建和初始化"""
    try:
        # 创建配置
        app_config = AppMonkeyConfig(
            name="taqu",
            text="hello please follow me and msg",
            text_publicly="hello everyone",
            count=5
        )
        
        # 创建上下文
        context = MonkeyContext()
        
        # 通过工厂创建他趣自动化脚本
        taqu_auto = MonkeyAutoFactory.create(app_config, context)
        
        print("✅ 他趣自动化脚本创建成功: {}".format(type(taqu_auto).__name__))
        print("✅ 包名: {}".format(taqu_auto.get_app_package_name()))
        print("✅ 配置名称: {}".format(taqu_auto.app_config.name))
        print("✅ 私聊文案: {}".format(taqu_auto.app_config.text))
        print("✅ 公屏文案: {}".format(taqu_auto.app_config.text_publicly))
        print("✅ 执行次数: {}".format(taqu_auto.app_config.count))
        
        return True
        
    except Exception as e:
        print("❌ 他趣自动化脚本测试失败: {}".format(e))
        import traceback
        traceback.print_exc()
        return False

def test_taqu_auto_with_chinese_name():
    """测试使用中文名称创建他趣自动化脚本"""
    try:
        # 创建配置（使用中文名称）
        app_config = AppMonkeyConfig(
            name="他趣",
            text="hello please follow me and msg",
            text_publicly="hello everyone",
            count=10
        )
        
        # 创建上下文
        context = MonkeyContext()
        
        # 通过工厂创建他趣自动化脚本
        taqu_auto = MonkeyAutoFactory.create(app_config, context)
        
        print("✅ 他趣自动化脚本（中文名）创建成功: {}".format(type(taqu_auto).__name__))
        
        return True
        
    except Exception as e:
        print("❌ 他趣自动化脚本（中文名）测试失败: {}".format(e))
        import traceback
        traceback.print_exc()
        return False

def test_taqu_auto_with_capital_name():
    """测试使用大写名称创建他趣自动化脚本"""
    try:
        # 创建配置（使用大写名称）
        app_config = AppMonkeyConfig(
            name="Taqu",
            text="hello please follow me and msg",
            text_publicly="hello everyone",
            count=3
        )
        
        # 创建上下文
        context = MonkeyContext()
        
        # 通过工厂创建他趣自动化脚本
        taqu_auto = MonkeyAutoFactory.create(app_config, context)
        
        print("✅ 他趣自动化脚本（大写名）创建成功: {}".format(type(taqu_auto).__name__))
        
        return True
        
    except Exception as e:
        print("❌ 他趣自动化脚本（大写名）测试失败: {}".format(e))
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试他趣自动化脚本...")
    print("=" * 50)
    
    success1 = test_taqu_auto()
    print()
    success2 = test_taqu_auto_with_chinese_name()
    print()
    success3 = test_taqu_auto_with_capital_name()
    
    print()
    print("=" * 50)
    if success1 and success2 and success3:
        print("🎉 所有测试通过！他趣自动化脚本已成功创建。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
