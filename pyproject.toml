[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "beautycall-monkey"
version = "1.0.0"
description = "BeautyCall Monkey Test"
requires-python = ">=3.8"
dependencies = [
    "uiautomator2>=2.16.0",
    "schedule>=1.2.0",
    "requests>=2.25.1",
]

[tool.setuptools]
package-dir = {"" = "src"}
packages = [
    "six",
]

[tool.setuptools.package-data]
"*" = ["*.py"] 