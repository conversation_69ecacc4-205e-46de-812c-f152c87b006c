#!/usr/bin/env python3
"""
TTYY自动化脚本测试文件
用于验证TTYY自动化脚本是否能正确导入和初始化
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext
from monkey_auto_factory import MonkeyAutoFactory

def test_ttyy_auto():
    """测试TTYY自动化脚本的创建和初始化"""
    try:
        # 创建配置
        app_config = AppMonkeyConfig(
            name="ttyy",
            text="测试私聊消息",
            text_publicly="测试公屏消息",
            count=5
        )
        
        # 创建上下文
        context = MonkeyContext()
        
        # 通过工厂创建TTYY自动化脚本
        ttyy_auto = MonkeyAutoFactory.create(app_config, context)
        
        print(f"✅ TTYY自动化脚本创建成功: {type(ttyy_auto).__name__}")
        print(f"✅ 包名: {ttyy_auto.get_app_package_name()}")
        print(f"✅ 配置名称: {ttyy_auto.app_config.name}")
        print(f"✅ 私聊文案: {ttyy_auto.app_config.text}")
        print(f"✅ 公屏文案: {ttyy_auto.app_config.text_publicly}")
        print(f"✅ 执行次数: {ttyy_auto.app_config.count}")
        
        return True
        
    except Exception as e:
        print(f"❌ TTYY自动化脚本测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ttyy_auto_with_chinese_name():
    """测试使用中文名称创建TTYY自动化脚本"""
    try:
        # 创建配置（使用中文名称）
        app_config = AppMonkeyConfig(
            name="TTYY",
            text="测试私聊消息",
            text_publicly="测试公屏消息",
            count=10
        )
        
        # 创建上下文
        context = MonkeyContext()
        
        # 通过工厂创建TTYY自动化脚本
        ttyy_auto = MonkeyAutoFactory.create(app_config, context)
        
        print(f"✅ TTYY自动化脚本（中文名）创建成功: {type(ttyy_auto).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ TTYY自动化脚本（中文名）测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始测试TTYY自动化脚本...")
    print("=" * 50)
    
    success1 = test_ttyy_auto()
    print()
    success2 = test_ttyy_auto_with_chinese_name()
    
    print()
    print("=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！TTYY自动化脚本已成功创建。")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
