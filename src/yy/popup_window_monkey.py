import time

from util.logger import log_print


class PopupWindowMonkey:
    def __init__(self, d, tag):
        self.d = d
        self.tag = tag

    def dismissYoungerWindow(self) -> bool:
        younger = self.d(resourceId="com.duowan.mobile:id/layout_young_tips")
        if younger.exists:
            self.d.press("back")
            log_print(f"{self.tag} dismissYoungerWindow success!")
            return True
        return False

    #主播拍了怕你希望回复弹窗
    def dismissAnchorHelloWindow(self) -> bool:
        hello = self.d(text="打个招呼")
        head = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/anchor_head_img")
        if hello.exists and head.exists:
            btnClose = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/anonymity_close")
            if btnClose.exists:
                btnClose.click()
            else:
                self.d.press("back")
            log_print(f"{self.tag} dismissAnchorHelloWindow success!")
            return True
        return False

    #场景礼物弹窗
    def dismissSceneGiftWindow(self) -> bool:
        viewPager = self.d(resourceId="com.duowan.mobile.entlive:id/scene_gift_viewpager")
        if viewPager.exists:
            btnClose = self.d(resourceId="com.duowan.mobile.entlive:id/scene_gift_pager_close")
            if btnClose.exists:
                btnClose.click()
            else:
                self.d.press("back")
            log_print(f"{self.tag} dismissSceneGiftWindow success!")
            return True
        return False

    #添加桌面组件弹窗
    def dismissDesktopWindow(self) -> bool:
        desktop = self.d(text="添加桌面组件")
        if desktop.exists:
            btnClose = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/desktop_tip_close")
            if btnClose.exists:
                btnClose.click()
            else:
                self.d.press("back")
            log_print(f"{self.tag} dismissDesktopWindow success!")
            return True
        return False


    #通用弹窗.
    def dismissPopImageWindow(self) -> bool:
        imgBg = self.d(resourceId="com.yy.mobile.plugin.main:id/popupImgBgLayout")
        if imgBg.exists:
            btnClose = self.d(resourceId="com.yy.mobile.plugin.main:id/activity_entrance_close")
            if btnClose.exists:
                btnClose.click()
            else:
                self.d.press("back")
            log_print(f"{self.tag} dismissPopImageWindow success!")
            return True
        return False

    # 首充惊喜弹窗
    def dismissWebWindow(self) -> bool:
        web = self.d(resourceId="cn.v6.xiuchang:id/web_view_h5")
        if web.exists:
            self.d.press("back")
            log_print(f"{self.tag} dismissWebWindow success!")
            time.sleep(1.8)
            return True
        return False

    def tryDismiss(self):
        while True:
            if (not self.dismissPopImageWindow()
                    and not self.dismissAnchorHelloWindow()
                    and not self.dismissDesktopWindow()
                    and not self.dismissSceneGiftWindow()
                    and not self.dismissYoungerWindow()):
                break
            else:
                #手Y会有弹层规范,关了一个弹窗会出下一个弹窗,这里先等下一个弹窗出来再检查.
                time.sleep(2)
                log_print(f"{self.tag} Close popupWindow success!")
        log_print(f"{self.tag} Close popupWindow finish no popup window found!")
