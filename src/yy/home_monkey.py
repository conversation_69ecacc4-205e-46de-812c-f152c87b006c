import time
from selectors import SelectSelector
from yy.popup_window_monkey import <PERSON><PERSON><PERSON><PERSON>ow<PERSON>onkey
from yy.room_monkey import Room<PERSON>onkey
from BaseMonkey import ActivityMonkey
from uiautomator2 import Device
from monkey_config import AppMonkeyConfig

class HomeMonkey(ActivityMonkey):

    def __init__(self, d: Device, app_config: AppMonkeyConfig):
        super().__init__(d, app_config, "com.yy.mobile.plugin.homepage.ui.home.HomeActivity")
        self.room = RoomMonkey(d, app_config)
        self.pm = PopupWindowMonkey(d, "HomeMonkey")

    def run(self) -> bool:
        print("In HomeActivity, wait 3 seconds...")
        time.sleep(3)
        currentInfo = self.d.app_current()
        print("Current activity: " + str(currentInfo["activity"]))
        if (currentInfo["activity"] == ".basemedia.watchlive.activity.LiveTemplateActivity"):
            print("In RoomControlActivity, run room monkey")
            self.room.run()
            return False

        self.pm.tryDismiss()
        roomlist = self.d(resourceId="com.duowan.mobile:id/living_horizontal_recyclerview")
        if not roomlist.exists:
            print("Couldn't find room list!")
            return False

        cardList = roomlist.child(resourceId="com.duowan.mobile:id/living_common_thumb")
        if len(cardList) > 0:
            cardList[0].click()
            print("try join room!")
            time.sleep(2)
            self.room.run()
        return False
