import time

from uiautomator2 import Direction, Device
from BaseMonkey import ActivityMonkey
from six.popup_window_monkey import PopupWindowMonkey
from util.app_util import is_app_foreground
from util.logger import log_print
from monkey_config import AppMonkeyConfig


INVITE_TEXT = """主播您好~我们有一款百万主播同款的微信视频美颜通话工具——秒颜
现在官方想选择邀请一些主播成为我们秒颜的会员 可以给您提供试用机会~
秒颜拥有【主播同款妆容、丰富的滤镜和头饰、手势特效】
可以让您在微信视频通话也自带直播间同款美颜
私下和大哥视频通话也会还原直播间的美貌
素颜也可以一键美颜上镜 妆容牢固不会掉
如果你愿意试用 可以用安卓手机搜索和下载“秒颜” 登录后填写邀请码：SuHu2"""

normal_dur = 2
ROOM_COUNT = 100
IS_TEST = False
class RoomMonkey(ActivityMonkey):

    def __init__(self, d: Device, app_config: AppMonkeyConfig):
        super().__init__(d, app_config, ".basemedia.watchlive.activity.LiveTemplateActivity")
        self.text = app_config.text
        self.room_count = app_config.count
        self.pm = PopupWindowMonkey(d, "RoomMonkey")


    #点击底部更多按钮
    def openMoreWindow(self) -> bool:
        #尝试关闭一下弹窗.
        self.pm.tryDismiss()
        time.sleep(1)
        btnMore = self.d(resourceId="com.duowan.mobile.entlive:id/layout_slide_menu_portrait")
        if btnMore.exists:
            btnMore.click()
            log_print("openMoreWindow success...")
            #等个2秒等弹窗显示出来
            time.sleep(1.5)
            return self.openPrivateChatWindow()
        else:
            log_print("openMoreWindow fail btnMore not found!")
        return False

    #打开私聊窗口
    def openPrivateChatWindow(self) -> bool:
        btnChat = self.d(text="私聊")
        if btnChat.exists:
            log_print("try openPrivateChatWindow...")
            btnChat.click()
            #等私聊弹窗起来.
            time.sleep(3)
            return self.sendChatMessage()
        else:
            log_print("openPrivateChatWindow fail btnChat not found!")
            self.closeMoreWindow()
        return False

    def sendChatMessage(self) -> bool:
        title = self.d(text="私聊记录")
        if not title.exists:
            log_print("openPrivateChatWindow fail!")
            #self.closeMoreWindow()
            return False
        log_print("openPrivateChatWindow success!")
        btnInput = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/et_input")
        if not btnInput.exists:
            log_print("sendMessage fail! PrivateChat text input not found!")
            self.closePrivateChatWindow()
            return False
        btnInput.click()
        btnInput.set_text(self.text)
        time.sleep(1.5)
        btnSend = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/btn_send")
        if btnSend.exists:
            log_print("try sendChatMessage...")
            #btnSend.click()
            time.sleep(2)
            result = False
            if not self.checkSendResult():
                log_print("sendChatMessage fail cause by privilege limit!")
            else:
                result = True
                log_print("sendChatMessage success!")
            self.closePrivateChatWindow()
            return result
        else:
            log_print("sendMessage fail! PrivateChat send button not found!")
            self.closePrivateChatWindow()
        return False

    def closeMoreWindow(self):
        self.d.press("back")

    def closePrivateChatWindow(self):
        back = self.d(resourceId="com.yy.mobile.plugin.livebasebiz:id/layout_fragment_lineat")
        if back.exists:
            back.click()
        else:
            self.d.press("back")
    
    def checkSendResult(self):
        d = self.d
        content = d(resourceId="com.yy.mobile.plugin.livebasebiz:id/right_message")
        if content.exists:
            contentText = content.info['text']
            log_print(f"checkSendResult result: {contentText == self.text} text: {contentText}")
            return contentText == self.text
        else:
            return False

    def joinNextRoom(self):
        d = self.d
        # touchLayout = d(resourceId="cn.v6.xiuchang:id/touch_layout")
        # # 获取View的边界坐标
        # bounds = touchLayout.info['bounds']
        # log_print("bounds: " + str(bounds))
        # center_x = (bounds['right'] - bounds['left']) / 2
        # center_y = (bounds['bottom'] - bounds['top']) / 2
        # log_print(f"joinNextRoom center_x: {center_x}, center_y: {center_y}")
        width, height = d.window_size()
        # d.swipe(width * 0.5, height * 0.5, width * 0.9, height * 0.5, 0.5)

        # 在View内部上滑（滑动距离为View高度的60%）
        # d.swipe(center_x, center_y + 200,  # 起点：中心点下移100px
        #         center_x, center_y - 200,  # 终点：中心点上移100px
        #         duration=1)
        # d.touch.down(width * 0.5, height * 0.5)  # Simulate press
        # time.sleep(0.1)  # Delay between down and move, control it yourself
        # d.touch.move( width * 0.5, height * 0.2, steps=20)
        # d.touch.up( width * 0.5, height * 0.2)  # Simulate release
        # d.swipe_ext(Direction.FORWARD)
        start_x, start_y = 500, 1500  # 起点
        end_x, end_y = 500, 500  # 终点
        # steps = 20  # 细分步数
        #
        # d.touch.down(start_x, start_y)
        # time.sleep(0.5)
        # for i in range(1, steps + 1):
        #     # 计算中间点
        #     x = start_x + (end_x - start_x) * i / steps
        #     y = start_y + (end_y - start_y) * i / steps
        #     d.touch.move(start_x, y)
        #     time.sleep(0.02)  # 控制每步间隔:ml-citation{ref="5" data="citationList"}
        # d.touch.up(end_x, end_y)
        # d.drag(start_x, start_y, end_x, end_y, 1)
        # d.swipe(0.8, 0.5, 0.8, 0.9)
        # 要加一点点的水平偏移才能实现上下滑,这App代码写得贼6啊,给你整了个防不胜防啊
        d.swipe(0.5, 0.5, 0.5, 0.1, 0.08)
        time.sleep(5)

    def checkAppForeground(self) -> bool:
        return is_app_foreground(self.d, 'com.duowan.mobile')

    def run(self) -> bool:
        success = 0
        fail = 0
        log_print("Start to send im chat message to " + str(self.room_count) + " rooms")
        for i in range(self.room_count):
            if not self.checkAppForeground():
                raise Exception("Target app is not in foreground!")
            #刷到了短剧了,直接跳过.
            if self.d(textContains="自动进入全屏观看").exists:
                log_print("ShortPlay channel hit, just ignore....")
                self.joinNextRoom()
                continue
            if self.d(resourceId="com.duowan.pluginyylove:id/tv_more_wonderful1").exists:
                log_print("YLove channel hit, just ignore....")
                self.joinNextRoom()
                continue
            elif self.openMoreWindow():
                success += 1
                log_print("Send im chat message success count: " + str(success) + ",fail count:" + str(fail))
            else:
                fail += 1
                log_print("Send im chat message fail count: " + str(fail) + ",success count: " + str(success))
            time.sleep(normal_dur)
            log_print("Join next room...")
            #d(scrollable=True).fling()
            self.pm.tryDismiss()
            self.joinNextRoom()
        return False
    
    def safeClick(self, btn, deep = 0):
        if btn.exists:
            try:
                btn.click()
                return True
            except Exception as e:
                log_print("Click button failed: " + str(e) + "deep: " + str(deep))
                if (deep == 2):
                    log_print("deep: " + str(deep) + "try to press back")
                    time.sleep(normal_dur)
                    self.d.press("back")
                    time.sleep(normal_dur)
                    
                if (deep >= 3):
                    return False
                else:
                    time.sleep(normal_dur)
                    return self.safeClick(btn, deep + 1)
        else:
            log_print("Couldn't find button!")
        return False