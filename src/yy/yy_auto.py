import time
import uiautomator2 as u2
from yy.room_monkey import <PERSON><PERSON>onkey
from yy.home_monkey import Home<PERSON>onkey
from util.logger import log_print
from BaseMonkey import <PERSON><PERSON><PERSON>
from monkey_config import AppMonkeyConfig
from uiautomator2 import Device

# yy自动脚本
class AutoYY(BaseAuto):
    def __init__(self, d: Device, app_config: AppMonkeyConfig):
        super().__init__(d, app_config)
        self.roomMonkey = RoomMonkey(self.d, app_config)
        self.homeMonkey = HomeMonkey(self.d, app_config)

    def run(self):
        self.d.app_stop('com.duowan.mobile')
        time.sleep(2)
        self.d.app_start('com.duowan.mobile')
        while True:
            info = self.d.app_current()
            print("Current activity: " + str(info["activity"]))

            if info["activity"] == "com.yy.mobile.plugin.homepage.login.FullScreenLoginActivity":
                print("Run script fail!, Please login first....")
                return False

            if info["activity"] == self.homeMonkey.activity:
                print("Found home activity, starting home run...")
                self.homeMonkey.run()
                break
            if info["activity"] == self.roomMonkey.activity:
                log_print("Found room activity, starting room run...")
                self.roomMonkey.run()
                break
            else:
                log_print("Waiting for target activity...")
                time.sleep(2)
