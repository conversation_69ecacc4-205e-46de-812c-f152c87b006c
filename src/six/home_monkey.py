import time
from selectors import SelectSelector
from six.popup_window_monkey import <PERSON>up<PERSON><PERSON>ow<PERSON>onkey
from six.room_monkey import Room<PERSON>onkey
from BaseMonkey import ActivityMonkey


class HomeMonkey(ActivityMonkey):

    def __init__(self, d):
        super().__init__(d, "cn.v6.sixrooms.ui.phone.HallActivity")
        self.room = RoomMonkey(d)
        self.pm = PopupWindowMonkey(d, "HomeMonkey")
        self.count = 0

    def run(self) -> bool:
        print("In HomeActivity, wait 3 seconds...")
        time.sleep(3)
        currentInfo = self.d.app_current()
        print("Current activity: " + str(currentInfo["activity"]))
        if (currentInfo["activity"] == "cn.v6.multivideo.activity.RoomControlActivity"):
            print("In RoomControlActivity, run room monkey")
            self.room.run()
            return False

        self.pm.tryDismiss()
        roomlist = self.d(resourceId="cn.v6.xiuchang:id/recyclerview")
        if not roomlist.exists:
            print("Couldn't find room list!")
            return False

        cardList = roomlist.child(resourceId="cn.v6.xiuchang:id/cardView")
        if len(cardList) > 0:
            cardList[0].click()
            print("try join room!")
            time.sleep(2)
            self.room.run()
        return False
