import time
import uiautomator2 as u2
from six.six_auto import AutoSix

def runApp():
    print("connect")
    d = u2.connect()
    print("app_stop cn.v6.xiuchang")
    d.app_stop('cn.v6.xiuchang')
    time.sleep(2)
    print("app_start cn.v6.xiuchang")
    d.app_start('cn.v6.xiuchang')

def run():
    runner = AutoSix()
    runner.run()

def runSix():
    runApp()
    run()

if __name__ == '__main__':
    #installApk()
    runApp()
    run()