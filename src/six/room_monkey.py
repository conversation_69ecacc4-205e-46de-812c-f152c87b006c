import time

from uiautomator2 import Direction, Device
from BaseMonkey import ActivityMonkey
from yy.popup_window_monkey import PopupWindowMonkey
from util.logger import log_print

INVITE_TEXT = """主播您好~我们有一款百万主播同款的微信视频美颜通话工具——秒颜
现在官方想选择邀请一些主播成为我们秒颜的会员 可以给您提供试用机会~
秒颜拥有【主播同款妆容、丰富的滤镜和头饰、手势特效】
可以让您在微信视频通话也自带直播间同款美颜
私下和大哥视频通话也会还原直播间的美貌
素颜也可以一键美颜上镜 妆容牢固不会掉
如果你愿意试用 可以用安卓手机搜索和下载“秒颜” 登录后填写邀请码：SuHu2"""

normal_dur = 2
ROOM_COUNT = 100
IS_TEST = False
class RoomMonkey(ActivityMonkey):

    def __init__(self, d):
        super().__init__(d, "cn.v6.multivideo.activity.RoomControlActivity")
        self.text = INVITE_TEXT
        self.room_count = ROOM_COUNT
        self.pm = PopupWindowMonkey(d, "RoomMonkey")


    def openChat(self) -> bool:
        #尝试关闭一下弹窗.
        self.pm.tryDismiss()
        if self.openChatListWindow():
            log_print("openChatListWindow success try open im chat window...")
            # 尝试关闭一下弹窗.
            self.pm.tryDismiss()
            if self.openChatWindow():
                log_print("openChatWindow success try send im message...")
                # 尝试关闭一下弹窗.
                self.pm.tryDismiss()
                return self.sendMessage()
            else:
                log_print("openChatWindow fail!")
                # 尝试关闭一下弹窗.
                self.pm.tryDismiss()
                self.closeChatWindow()
        else:
            log_print("openChatListWindow fail")
            # 尝试关闭一下弹窗.
            self.pm.tryDismiss()
            self.closeChatWindow()

        return False

    def closeChatWindow(self):
        d = self.d
        btnBack = d(resourceId="cn.v6.xiuchang:id/iv_back")
        if btnBack.exists:
            self.safeClick(btnBack)
            time.sleep(normal_dur)
        else:
            log_print("Couldn't find back button try send back press event...")
            d.press("back")
            time.sleep(normal_dur)

    def closeChatListWindow(self):
        d = self.d
        btnBack = d(resourceId="cn.v6.xiuchang:id/img_back")
        if btnBack.exists:
            self.safeClick(btnBack)
            time.sleep(normal_dur)
        else:
            log_print("Couldn't find back button try send back press event...")
            d.press("back")
            time.sleep(normal_dur)

    def openChatListWindow(self) -> bool:
        d = self.d
        imBtn = d(resourceId="cn.v6.xiuchang:id/iv_private_msg")
        if imBtn.exists:
            log_print("find chat button try click...")
            self.safeClick(imBtn)
            time.sleep(normal_dur)
            list = d(resourceId="cn.v6.xiuchang:id/rc_conversation_list")
            return list.exists
        else:
            log_print("Couldn't find im button!")
        return False

    def openChatWindow(self) -> bool:
        d = self.d
        btnHost = d(resourceId="cn.v6.xiuchang:id/rc_conversation_list").child(
            classNameMatches=".*ViewGroup", index=0)
        if btnHost.exists:
            self.safeClick(btnHost)
            time.sleep(normal_dur)
            return True
        else:
            log_print("Couldn't find host button!")
        return False

    def sendMessage(self) -> bool:
        d = self.d
        btnInput = d(resourceId="cn.v6.xiuchang:id/edit_btn")
        if btnInput.exists:
            self.safeClick(btnInput)
            time.sleep(normal_dur)
            btnInput.set_text(self.text)
            btnSend = d(resourceId="cn.v6.xiuchang:id/input_panel_add_or_send")
            if btnSend.exists:
                if not IS_TEST:
                    self.safeClick(btnSend)
                time.sleep(3)
                log_print("press back to dismiss input panel")
                d.press("back")
                time.sleep(normal_dur)
                sendResult = self.checkSendResult()
                if sendResult:
                    log_print("Send im chat success!")
                else:
                    log_print("Send im chat fail!")
                self.closeChatWindow()
                log_print("closeChatWindow")
                self.closeChatListWindow()
                log_print("closeChatListWindow")
                return sendResult
            else:
                log_print("Couldn't find send button!")
        else:
            log_print("Couldn't find input button!")
        return False
    
    def checkSendResult(self):
        d = self.d
        content = d(resourceId="cn.v6.xiuchang:id/rc_text")
        if content.exists:
            contentText = content.info['text']
            log_print(f"checkSendResult result: {contentText == self.text} text: {contentText}")
            warn = d(resourceId="cn.v6.xiuchang:id/rc_warning")
            if warn.exists:
                log_print(f"checkSendResult warn exist, send fail")
                return False
            return contentText == self.text
        else:
            return False

    def joinNextRoom(self):
        d = self.d
        # touchLayout = d(resourceId="cn.v6.xiuchang:id/touch_layout")
        # # 获取View的边界坐标
        # bounds = touchLayout.info['bounds']
        # log_print("bounds: " + str(bounds))
        # center_x = (bounds['right'] - bounds['left']) / 2
        # center_y = (bounds['bottom'] - bounds['top']) / 2
        # log_print(f"joinNextRoom center_x: {center_x}, center_y: {center_y}")
        width, height = d.window_size()
        # d.swipe(width * 0.5, height * 0.5, width * 0.9, height * 0.5, 0.5)

        # 在View内部上滑（滑动距离为View高度的60%）
        # d.swipe(center_x, center_y + 200,  # 起点：中心点下移100px
        #         center_x, center_y - 200,  # 终点：中心点上移100px
        #         duration=1)
        # d.touch.down(width * 0.5, height * 0.5)  # Simulate press
        # time.sleep(0.1)  # Delay between down and move, control it yourself
        # d.touch.move( width * 0.5, height * 0.2, steps=20)
        # d.touch.up( width * 0.5, height * 0.2)  # Simulate release
        # d.swipe_ext(Direction.FORWARD)
        start_x, start_y = 500, 1500  # 起点
        end_x, end_y = 500, 500  # 终点
        # steps = 20  # 细分步数
        #
        # d.touch.down(start_x, start_y)
        # time.sleep(0.5)
        # for i in range(1, steps + 1):
        #     # 计算中间点
        #     x = start_x + (end_x - start_x) * i / steps
        #     y = start_y + (end_y - start_y) * i / steps
        #     d.touch.move(start_x, y)
        #     time.sleep(0.02)  # 控制每步间隔:ml-citation{ref="5" data="citationList"}
        # d.touch.up(end_x, end_y)
        # d.drag(start_x, start_y, end_x, end_y, 1)
        # d.swipe(0.8, 0.5, 0.8, 0.9)
        # 要加一点点的水平偏移才能实现上下滑,这App代码写得贼6啊,给你整了个防不胜防啊
        d.swipe(0.5, 0.5, 0.51, 0.1)
        time.sleep(7)

    def run(self) -> bool:
        success = 0
        fail = 0
        log_print("Start to send im chat message to " + str(self.room_count) + " rooms")
        for i in range(self.room_count):
            if self.openChat():
                success += 1
                log_print("Send im chat message success count: " + str(success))
            else:
                fail += 1
                log_print("Send im chat message fail count: " + str(fail))
            time.sleep(normal_dur)
            log_print("Join next room...")
            #d(scrollable=True).fling()
            self.pm.tryDismiss()
            self.joinNextRoom()
        return False
    
    def safeClick(self, btn, deep = 0):
        if btn.exists:
            try:
                btn.click()
                return True
            except Exception as e:
                log_print("Click button failed: " + str(e) + "deep: " + str(deep))
                if (deep == 2):
                    log_print("deep: " + str(deep) + "try to press back")
                    time.sleep(normal_dur)
                    self.d.press("back")
                    time.sleep(normal_dur)
                    
                if (deep >= 3):
                    return False
                else:
                    time.sleep(normal_dur)
                    return self.safeClick(btn, deep + 1)
        else:
            log_print("Couldn't find button!")
        return False