import time

from util.logger import log_print


class PopupWindowMonkey:
    def __init__(self, d, tag):
        self.d = d
        self.tag = tag

    def dismissYoungerWindow(self) -> bool:
        younger = self.d(resourceId="cn.v6.xiuchang:id/younger_layout")
        if younger.exists:
            self.d.press("back")
            log_print(f"{self.tag} dismissYoungerWindow success!")
            return True
        return False

        # 设置昵称弹窗
    def dismissNameWindow(self) -> bool:
        content = self.d(resourceId="cn.v6.xiuchang:id/cs_content")
        if content.exists:
            btnClose = self.d(resourceId="cn.v6.xiuchang:id/iv_close")
            if btnClose.exists:
                btnClose.click()
                log_print(f"{self.tag} dismissNameWindow success!")
                time.sleep(1.8)
                return True
        return False

    # 首充惊喜弹窗
    def dismissWebWindow(self) -> bool:
        web = self.d(resourceId="cn.v6.xiuchang:id/web_view_h5")
        if web.exists:
            self.d.press("back")
            log_print(f"{self.tag} dismissWebWindow success!")
            time.sleep(1.8)
            return True
        return False

    def tryDismiss(self):
        while True:
            if (not self.dismissNameWindow()
                    and not self.dismissWebWindow()
                    and not self.dismissYoungerWindow()):
                break
            else:
                log_print(f"{self.tag} Close popupWindow success!")
        log_print(f"{self.tag} Close popupWindow finish no popup window found!")
