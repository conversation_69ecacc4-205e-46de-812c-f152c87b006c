import time
import uiautomator2 as u2
from six.room_monkey import <PERSON><PERSON>onkey
from six.home_monkey import HomeMonkey
from util.logger import log_print

# 六间房自动脚本
class AutoSix:
    def __init__(self):
        self.d = u2.connect()
        self.roomMonkey = RoomMonkey(self.d)
        self.homeMonkey = HomeMonkey(self.d)

    def run(self):
        while True:
            info = self.d.app_current()
            print("Current activity: " + str(info["activity"]))

            if info["activity"] == self.homeMonkey.activity:
                print("Found home activity, starting home run...")
                self.homeMonkey.run()
                break
            if info["activity"] == self.roomMonkey.activity:
                log_print("Found room activity, starting room run...")
                self.roomMonkey.run()
                break
            if info["activity"] == "cn.v6.sixrooms.dialog.hall.HallYoungerDialogActivity":
                # 青少年模式弹窗
                log_print("Found HallYoungerDialogActivity, back...")
                self.d.press("back")
                time.sleep(2)
                continue
            if info["activity"] == "cn.v6.sixrooms.ui.phone.UpGradeActivity":
                # 升级弹窗
                log_print("Found UpGradeActivity, back...")
                self.d.press("back")
                time.sleep(2)
                continue
            else:
                log_print("Waiting for target activity...")
                time.sleep(2)
