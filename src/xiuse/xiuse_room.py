import time
from BaseMonkey import ActivityMonkey
from util.logger import log_print
from uiautomator2 import Device
from xiuse.xiuse_dialog import XiuseDialog


class XiuseRoom(ActivityMonkey):
    def __init__(self, context, app_config):
        super().__init__(context, app_config, "com.showself.ui/.PullStreamActivity")
        self.text = app_config.text
        self.count = app_config.count
        self.is_test = True
        self.dialog = XiuseDialog(self.d)

    def _safe_ui_action(self, d, ui_object, action_type, log_msg, text_to_set=None) -> bool:
        """
        通用的安全UI操作方法，处理点击和设置文本，支持uiautomator2和adb备用。
        action_type: 'click' 或 'set_text'
        """
        if not ui_object.exists:
            log_print(f"[ERROR] 执行 {log_msg} 失败: 元素不存在!")
            return False

        try:
            if action_type == 'click':
                if self.dialog.closeNetworkDialog():
                    time.sleep(1)
                ui_object.click()
                log_print(f"[SUCCESS] 成功 {log_msg}")
            elif action_type == 'set_text':
                if self.dialog.closeNetworkDialog():
                    time.sleep(1)
                ui_object.set_text(text_to_set)
                log_print(f"[SUCCESS] 成功 {log_msg}: '{text_to_set}'")
            return True
        except Exception as e:
            log_print(f"[WARN] 执行 {log_msg} 失败 (uiautomator2): {e}，尝试adb备用方案。")
            bounds = ui_object.info.get("bounds")
            if bounds:
                import os
                serial = d.serial
                x = (bounds["left"] + bounds["right"]) // 2
                y = (bounds["top"] + bounds["bottom"]) // 2
                os.system(f"adb -s {serial} shell input tap {x} {y}")
                log_print(f"[INFO] 通过adb备用方案 {log_msg} 点击。")

                if action_type == 'set_text':
                    # Adb tap opens keyboard, but doesn't set text. Try uiautomator2 set_text again.
                    time.sleep(0.5)  # 给一点时间让键盘弹出
                    if ui_object.exists:  # Re-check existence after adb tap
                        try:
                            ui_object.set_text(text_to_set)
                            log_print(
                                f"[SUCCESS] 通过adb点击后，重新设置文本成功 ({log_msg}): '{text_to_set}'")
                            return True
                        except Exception as inner_e:
                            log_print(
                                f"[ERROR] 通过adb点击后，设置文本仍然失败 ({log_msg}): {inner_e}")
                            return False
                    else:
                        log_print(f"[ERROR] adb点击后，元素消失或仍无法找到 ({log_msg})!")
                        return False
                return True  # Adb click was successful for just click action
            else:
                log_print(f"[ERROR] 无法获取 {log_msg} 元素的坐标进行adb点击。")
                return False

    def closeChatWindow(self, d):
        btn_nav_left = d(resourceId="com.showself.ui:id/btn_nav_left")
        if btn_nav_left.exists:
            self._safe_ui_action(d, btn_nav_left, 'click', "关闭聊天窗口按钮")
        else:
            log_print("找不到返回按钮，发送返回事件: 关闭聊天窗口")
            d.press("back")

    def closeChatListWindow(self, d):
        btnBack = d(resourceId="com.showself.ui:id/img_back")
        if btnBack.exists:
            self._safe_ui_action(d, btnBack, 'click', "关闭私聊列表按钮")
        else:
            log_print("找不到返回按钮，发送返回事件: 关闭私聊列表")
            d.press("back")

    def close_webview_dialogs(self, d):
        log_print("尝试关闭WebView弹窗...")
        webview_dialog = d(
            resourceId="com.showself.ui:id/h5_common_dialog_webView")
        if webview_dialog.exists:
            close_button = d(
                resourceId="com.showself.ui:id/h5_common_dialog_iv_close")
            if close_button.exists:
                if self._safe_ui_action(d, close_button, 'click', "WebView弹窗关闭按钮"):
                    time.sleep(1)  # 等待弹窗关闭
                    self.close_webview_dialogs(d)  # 递归调用，检查是否有下一个弹窗
            else:
                log_print("WebView弹窗存在，但找不到关闭按钮！")
        else:
            log_print("未找到WebView弹窗。")

    def open_chat_window(self, d) -> bool:
        time.sleep(3)  # 增加等待时间，确保页面加载完成
        if self.dialog.closeNetworkDialog():
            time.sleep(1)
        anchor_chat = d(resourceId="com.showself.ui:id/rvMsgList")
        if anchor_chat.exists:
            first_child = anchor_chat.child(index=0)
            if first_child.exists:
                time.sleep(1)  # 增加等待时间，确保子元素加载完成
                if not self._safe_ui_action(d, first_child, 'click', "rvMsgList第一个子View"):
                    log_print("点击rvMsgList第一个子View失败，返回False")
                    return False

                time.sleep(1)
                has_send = False  # 是否已经发送过消息
                rvChat = d(resourceId="com.showself.ui:id/rvChat")
                if rvChat.exists:
                    log_print("找到聊天内容列表控件")
                    chatItemCount = 0
                    while True:
                        child = rvChat.child(index=chatItemCount)
                        if child.exists:
                            log_print(f"找到聊天内容列表第{chatItemCount}个元素")
                            chat_text = child.child(
                                resourceId="com.showself.ui:id/tv_chat_me_content")
                            if chat_text.exists:
                                # 获取文本内容
                                text_content = chat_text.get_text()
                                log_print(f"聊天内容: {text_content}")
                                if text_content == self.text:
                                    has_send = True
                                    break
                            chatItemCount += 1
                        else:
                            break

                if has_send:
                    log_print("已经发送过消息，返回False")
                    d.press("back")
                    time.sleep(1)
                    return False

                if self.sendMessage(d):
                    return True
                else:
                    log_print("发送消息失败，返回False")
                    d.press("back")
                    time.sleep(1)
                    return False
            else:
                log_print("找不到聊天列表第一个元素!")
                d.press("back")
                return False
        else:
            log_print("找不到聊天列表!")
            d.press("back")
            return False

    def handle_chat_affairs(self, d) -> bool:
        chat_btn = d(resourceId="com.showself.ui:id/icon_show_chat")
        if chat_btn.exists:
            if not self._safe_ui_action(d, chat_btn, 'click', "私聊按钮"):
                log_print("点击私聊按钮失败，返回False")
                return False

            if self.open_chat_window(d):
                return True
            else:
                log_print("打开聊天窗口或发送消息失败，返回False")
                return False
        else:
            log_print("找不到私聊按钮 (icon_show_chat)! 返回False")
            return False

    def sendMessage(self, d) -> bool:
        time.sleep(2)  # 增加等待时间，确保输入框可用
        edit_text = d(resourceId="com.showself.ui:id/edit_text")
        if edit_text.exists:
            log_print("当前UI层次结构 (sendMessage - edit_text存在时): True")

            if not self._safe_ui_action(d, edit_text, 'click', "输入框"):
                log_print("点击输入框失败，返回False")
                return False

            if not self._safe_ui_action(d, edit_text, 'set_text', "设置输入框文本", self.text):
                log_print("设置输入框文本失败，返回False")
                return False

            btn_send_message = d(
                resourceId="com.showself.ui:id/btn_send_message")
            if btn_send_message.exists:
                if not self.is_test:
                    if not self._safe_ui_action(d, btn_send_message, 'click', "发送按钮"):
                        log_print("点击发送按钮失败，返回False")
                log_print("点击发送按钮成功，等待2秒")
                time.sleep(2)

                sendResult = self.checkSendResult(d)
                if not sendResult:
                    log_print("检测发送失败，返回False")

                time.sleep(self.action_long_dur)
                self.closeChatWindow(d)
                time.sleep(1)
                self.closeChatListWindow(d)
                return sendResult
            else:
                log_print("找不到发送按钮! 返回False")
                return False
        else:
            log_print("找不到输入按钮! 返回False")
            return False

    def checkSendResult(self, d: Device) -> bool:
        rvChat = d(resourceId="com.showself.ui:id/rvChat")
        if rvChat.exists:
            chatItemCount = 0
            while True:
                child = rvChat.child(index=chatItemCount)
                if child.exists:
                    log_print(f"找到聊天内容列表第{chatItemCount}个元素")
                    chat_text = child.child(
                        resourceId="com.showself.ui:id/tv_chat_me_content")
                    if chat_text.exists:
                        # 获取文本内容
                        text_content = chat_text.get_text()
                        if text_content == self.text:
                            fail_ui = child.child(
                                resourceId="com.showself.ui:id/view_message_state_failed")
                            if fail_ui.exists:
                                return False
                            else:
                                return True
                    chatItemCount += 1
                else:
                    break
            return True

    def joinNextRoom(self, d):
        width, height = d.window_size()
        # 调整滑动参数，使滑动距离更长，从屏幕下部向上滑动，持续0.3秒
        start_x, start_y = width * 0.5, height * 0.8
        end_x, end_y = width * 0.5, height * 0.3
        duration = 0.3  # 滑动持续时间，单位秒
        d.swipe(start_x, start_y, end_x, end_y, duration)
        log_print(
            f"滑动直播间页面: 从 ({start_x:.0f}, {start_y:.0f}) 到 ({end_x:.0f}, {end_y:.0f})，持续 {duration} 秒。")

    def run(self, activity, d) -> bool:
        log_print("activity" + str(activity))

        if ".PullStreamActivity" in activity:
            log_print("直播间页面")
            success_count = 0
            fail_count = 0
            for i in range(self.count):
                log_print("进入第 " + str(i+1) + " 个直播间!")
                if self.dialog.closeNetworkDialog():
                    time.sleep(1)
                if self.handle_chat_affairs(d):
                    success_count += 1
                    log_print(f"处理聊天事务成功! 成功次数: {success_count}")
                else:
                    fail_count += 1
                    log_print(f"处理聊天事务失败! 失败次数: {fail_count}")
                    self.close_webview_dialogs(d)
                    self.handle_chat_affairs(d)

                self.joinNextRoom(d)
                time.sleep(7)  # 在进入下一个房间操作后，增加5秒的等待时间
        else:
            log_print("找不到直播间页面..")
        return False
