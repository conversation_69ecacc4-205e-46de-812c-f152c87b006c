import time

from BaseMonkey import BaseAuto
from monkey_config import AppMonkeyConfig
from xiuse.xiuse_home import <PERSON>useH<PERSON>
from util.logger import log_print
from models.monkey_context import MonkeyContext

# 秀色直播自动脚本


class AutoXiuse(BaseAuto):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.m = XiuseHome(context, app_config)

    def run(self):
        self.d.app_stop('com.showself.ui')
        time.sleep(self.action_normal_dur)
        self.d.app_start('com.showself.ui')
        try:
            info = self.d.app_current()
            log_print("应用信息: " + str(info))
            activity = info["activity"]

            # 处理掉闪屏页
            if "LoadingActivity" in activity:
                log_print("检测到闪屏页，等待加载完成...")
                skip_btn = self.d(resourceId="com.showself.ui:id/tv_ad_pic")
                if skip_btn.wait(timeout=5):  # 等待按钮出现
                    try:
                        skip_btn.click()
                        log_print("成功点击跳过按钮")
                    except Exception as e:
                        log_print(f"点击跳过按钮失败 (uiautomator2): {e}，尝试adb点击")
                        bounds = skip_btn.info.get("bounds")
                        if bounds:
                            import os
                            serial = self.d.serial
                            x = (bounds["left"] + bounds["right"]) // 2
                            y = (bounds["top"] + bounds["bottom"]) // 2
                            os.system(
                                f"adb -s {serial} shell input tap {x} {y}")
                            log_print("通过adb点击跳过按钮")
                        else:
                            log_print("无法获取跳过按钮的坐标进行adb点击")
                    # time.sleep(2) # 等待页面跳转
                else:
                    log_print("未找到跳过按钮")

                # 重新获取当前页面的Activity，确保进入正确页面
                info = self.d.app_current()
                log_print("应用信息: " + str(info))
                activity = info["activity"]

            self.m.run(activity, self.d)
        except Exception as e:
            log_print(f"程序运行过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
