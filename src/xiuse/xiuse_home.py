import time
from BaseMonkey import ActivityMonkey
from util.logger import log_print


class XiuseHome(ActivityMonkey):
    def __init__(self, context, app_config):
        super().__init__(context, app_config, "com.showself.ui/.HomeActivity")
        self.room = None  # 延迟初始化

    def closeYoungModeDialogIfExists(self, d):
        tvTitle = d(textContains="进入青少年模式")
        if tvTitle.exists:
            tvDismiss = d(resourceId="com.showself.ui:id/tv_dismiss")
            if tvDismiss.exists:
                try:
                    tvDismiss.click()
                    log_print("关闭青少年模式对话框")
                except Exception as e:
                    log_print(f"点击青少年模式对话框失败 (uiautomator2): {e}，尝试adb点击")
                    bounds = tvDismiss.info.get("bounds")
                    if bounds:
                        import os
                        serial = d.serial
                        x = (bounds["left"] + bounds["right"]) // 2
                        y = (bounds["top"] + bounds["bottom"]) // 2
                        os.system(f"adb -s {serial} shell input tap {x} {y}")
                        log_print("通过adb点击关闭青少年模式对话框")
                    else:
                        log_print("无法获取青少年模式对话框的坐标进行adb点击")
            else:
                log_print("无法关闭青少年模式对话框!")

    def openChatListWindow(self, d) -> bool:
        imBtn = d(resourceId="com.showself.ui:id/iv_private_msg")
        if imBtn.exists:
            imBtn.click()
            time.sleep(2)
            list = d(resourceId="com.showself.ui:id/rc_conversation_list")
            return list.exists
        else:
            log_print("找不到私聊按钮!")
        return False

    def closeChatListWindow(self, d):
        time.sleep(1.5)
        btnBack = d(resourceId="com.showself.ui:id/img_back")
        if btnBack.exists:
            btnBack.click()
        else:
            log_print("找不到返回按钮，尝试发送返回事件...")
            d.press("back")

    def enterLiveRoom(self, activity, d):
        if self.room is None:
            from xiuse.xiuse_room import XiuseRoom
            self.room = XiuseRoom(d, self.app_config)

        # 首页一般有Banner，尝试直接定位第1个房间元素
        home_item = d(
            resourceId="com.showself.ui:id/room_theme_rl1", instance=1)

        if home_item.exists:
            try:
                home_item.click()
                log_print("点击第1个直播间成功!")
            except Exception as e:
                log_print(f"点击直播间失败 (uiautomator2): {e}，尝试adb点击")
                bounds = home_item.info.get("bounds")
                if bounds:
                    import os
                    serial = d.serial
                    x = (bounds["left"] + bounds["right"]) // 2
                    y = (bounds["top"] + bounds["bottom"]) // 2
                    os.system(f"adb -s {serial} shell input tap {x} {y}")
                    log_print("通过adb点击直播间成功")
                else:
                    log_print("无法获取直播间元素的坐标进行adb点击")

            time.sleep(1)
            # 点击房间后，重新获取当前页面的Activity
            current_activity_info = d.app_current()
            current_activity = current_activity_info["activity"]
            log_print(f"点击直播间后，当前页面: {current_activity}")
            self.room.run(current_activity, d)
            return True
        else:
            log_print("未找到或无法点击第1个直播间")

        return False

    def run(self, activity, d) -> bool:
        log_print("当前页面: " + activity)

        self.closeYoungModeDialogIfExists(d)

        time.sleep(3)

        if ".HomeActivity" in activity:
            log_print("进入首页")
            if self.enterLiveRoom(activity, d):
                return True
            else:
                log_print("无法进入直播间")
        else:
            log_print("找不到首页...")
        return False
