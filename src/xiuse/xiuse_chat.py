import time
from util.logger import log_print


class XiuseChat:
    def __init__(self):
        self.text = "测试消息"

    def sendMessage(self, d) -> bool:
        time.sleep(2)
        btnInput = d(resourceId="com.showself.ui:id/edit_btn")
        if btnInput.exists:
            btnInput.click()
            time.sleep(2)
            btnInput.set_text(self.text)
            time.sleep(2)
            btnSend = d(resourceId="com.showself.ui:id/input_panel_add_or_send")
            if btnSend.exists:
                log_print("发送私聊消息成功!")
                return True
            else:
                log_print("找不到发送按钮!")
        else:
            log_print("找不到输入按钮!")
        return False

    def closeChatWindow(self, d):
        time.sleep(1.5)
        btnBack = d(resourceId="com.showself.ui:id/iv_back")
        if btnBack.exists:
            btnBack.click()
        else:
            log_print("找不到返回按钮，尝试发送返回事件...")
            d.press("back")

    def run(self, activity, d) -> bool:
        if "ChatNewActivity" in activity:
            log_print("私聊页面")
            success = 0
            fail = 0
            for i in range(100):
                if self.sendMessage(d):
                    success += 1
                    log_print("发送私聊消息成功次数: " + str(success))
                else:
                    fail += 1
                    log_print("发送私聊消息失败次数: " + str(fail))
                time.sleep(3)
        else:
            log_print("找不到私聊页面..")
        return False 