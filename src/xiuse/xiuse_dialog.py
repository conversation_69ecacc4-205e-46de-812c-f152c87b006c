from uiautomator2 import Device
from util.logger import log_print

# 秀色直播弹窗处理
class XiuseDialog:
    def __init__(self, d: Device):
        self.d = d

    def closeNetworkDialog(self) -> bool:
        network_dialog = self.d(resourceId="com.showself.ui:id/tv_custom_notice_dialog_note")
        if network_dialog.exists:
            self.d.press("back")
            log_print("关闭网络检测弹窗")
            return True
        else:
            return False
 