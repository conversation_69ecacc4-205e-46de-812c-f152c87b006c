import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


class HeziDialog(BaseDialog):
    def __init__(self, context):
        super().__init__(context)

    # 权限弹窗
    def close_permission(self):
        permission_dialog = self.d(
            resourceId="com.android.permissioncontroller:id/perm_desc_root"
        )
        if permission_dialog.exists:
            log_print("找到权限弹窗，点击确定")
            deny_view = self.d(
                resourceId="com.android.permissioncontroller:id/permission_deny_button"
            )
            result = deny_view.click_exists()
            if not result:
                deny_view = self.d(
                    resourceId="com.android.permissioncontroller:id/permission_deny_and_dont_ask_again_button"
                )
                result = deny_view.click_exists()
            log_print(f"权限弹窗点击结果:{result}")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_permission_dialog2(self):
        permission_dialog = self.d(
            resourceId="com.android.permissioncontroller:id/grant_singleton"
        )
        if permission_dialog.exists:
            log_print("找到权限grant_singleton弹窗，点击确定")
            deny_view = self.d(
                resourceId="com.android.permissioncontroller:id/permission_deny_and_dont_ask_again_button"
            )
            deny_view.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    # 未知Flutter弹窗
    def close_unknown_flutter_dialog(self):
        unknown_flutter_dialog = self.d(resourceId="com.immomo.vchat:id/flutter")
        if unknown_flutter_dialog.exists:
            log_print("找到未知Flutter弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_leave_micro_and_join_room(self):
        leave_micro_and_join_room = self.d(
            resourceId="com.immomo.vchat:id/common_confirm_dialog_center_layout"
        )
        if leave_micro_and_join_room.exists:
            log_print("找到离开麦位并加入房间弹窗，点击确定")
            confirm_btn = self.d(
                resourceId="com.immomo.vchat:id/common_confirm_dialog_confirm"
            )
            confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_young_mode_dialog(self):
        young_mode_dialog = self.d.xpath('//*[@text="进入青少年模式"]')
        if young_mode_dialog.exists:
            log_print("找到青少年模式弹窗，点击我知道了")
            confirm_btn = self.d(resourceId="com.immomo.vchat:id/btn_close")
            confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_activity_banner_dialog(self):
        activity_banner_dialog = self.d(
            resourceId="com.immomo.vchat:id/room_activity_banner_layout"
        )
        if activity_banner_dialog.exists:
            log_print("找到活动banner弹窗，点击关闭")
            close_btn = self.d(
                resourceId="com.immomo.vchat:id/room_activity_banner_close_icon"
            )
            close_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_recommend_join_room_dialog(self):
        recommend_join_room_dialog = self.d(
            resourceId="com.immomo.vchat:id/room_recommend_join_background_image"
        )
        if recommend_join_room_dialog.exists:
            log_print("找到推荐加入房间弹窗，点击关闭")
            close_btn = self.d(
                resourceId="com.immomo.vchat:id/room_recommend_join_close"
            )
            close_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_permission_device_dialog(self):
        permission_dialog = self.d(
            resourceId="com.android.permissioncontroller:id/grant_singleton"
        )
        if permission_dialog.exists:
            log_print("找到权限弹窗")
            if self.d.xpath('//*[contains(@text, "设备")]').exists:
                log_print("找到设备权限弹窗，点击禁止")
                deny_view = self.d(
                    resourceId="com.android.permissioncontroller:id/permission_deny_button"
                )
                deny_view.click_exists()
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未找到设备权限弹窗")
                return False
        else:
            return False

    def close_room_owner_notify_dialog(self):
        """房主须知弹窗"""
        room_owner_notify_dialog = self.d.xpath('//*[@text="房主须知"]')
        if room_owner_notify_dialog.exists:
            log_print("找到房主须知弹窗，点击我知道了")
            confirm_btn = self.d.xpath('//*[@text="我知道了"]')
            confirm_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_micro_permission_dialog(self):
        """录音权限弹窗"""
        # //*[@text="房间功能需要使用 "录音" 权限,请到 "应用信息 -> 权限" 中授予！"]
        micro_dialog = self.d.xpath('//*[contains(@text, "房间功能需要使用")]')
        if micro_dialog.exists:
            log_print("找到录音权限弹窗，点击取消")
            # 尝试找一下真的文案，打印日志排查问题
            try:
                content = self.d(resourceId="com.immomo.vchat:id/etv_content_halert")
                if content.exists:
                    log_print(f"录音权限弹窗文案: {content.get_text()}")
                else:
                    log_print("未找到录音权限弹窗文案")
            except Exception as e:
                log_print(f"获取录音权限弹窗文案失败: {e}")

            cancel_btn = self.d.xpath('//*[@text="取消"]')
            cancel_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_invite_join_micro_dialog(self):
        invite_join_micro_dialog = self.d.xpath('//*[@text="邀请你上麦聊天"]')
        if invite_join_micro_dialog.exists:
            log_print("找到邀请上麦弹窗，点击暂不上麦")
            cancel_btn = self.d(
                resourceId="com.immomo.vchat:id/common_confirm_dialog_cancel"
            )
            cancel_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_floating_window_permission_dialog(self):
        floating_window_permission_dialog = self.d.xpath(
            "//*[@text=\"检测到您没有开启'悬浮窗'权限,可能会导致功能不能正常使用,是否开启\"]"
        )
        if floating_window_permission_dialog.exists:
            log_print("找到悬浮窗权限弹窗，点击取消")
            confirm_btn = self.d.xpath('//*[@text="取消"]')
            confirm_btn.click_exists()
            return True
        else:
            return False

    def close_unknown_common_dialog(self):
        unknown_common_dialog = self.d(
            resourceId="com.immomo.vchat:id/root_content_halert"
        )
        if unknown_common_dialog.exists:
            log_print("找到未知错误弹窗，点击取消")
            confirm_btn = self.d(resourceId="com.immomo.vchat:id/btn_negative_halert")
            confirm_btn.click_exists()
            return True
        else:
            return False

    def process_login_activity(self):
        info = self.d.app_current()
        activity = info["activity"]
        if activity == "com.vchat.login.page.LoginNewActivity":
            log_print("检查到现在在登录页，终止运行")
            raise TerminatedMonkeyError.create_terminated_app_run_account_err(
                "账号异常-未登录"
            )
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_permission():
            result = True
        if self.close_permission_dialog2():
            result = True
        if self.close_unknown_flutter_dialog():
            result = True
        if self.close_young_mode_dialog():
            result = True
        if self.close_activity_banner_dialog():
            result = True
        if self.close_recommend_join_room_dialog():
            result = True
        if self.close_permission_device_dialog():
            result = True
        if self.close_leave_micro_and_join_room():
            result = True
        if self.close_room_owner_notify_dialog():
            result = True
        if self.close_micro_permission_dialog():
            result = True
        if self.close_invite_join_micro_dialog():
            result = True
        if self.close_floating_window_permission_dialog():
            result = True
        if self.close_unknown_common_dialog():
            result = True
        if self.process_login_activity():
            result = True
        return result
