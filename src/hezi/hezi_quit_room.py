import time
from BaseMonkey import BaseMonkey
from util.logger import log_print

# 退出直播间


class HeziQuitRoomMonkey(BaseMonkey):

    def run(self):
        back_btn = self.d(
            resourceId="com.immomo.vchat:id/iv_change_room_function")
        if self.retry_exists(back_btn, "查找直播间的back_btn"):
            log_print("点击返回按钮")
            self.safe_click(back_btn, "点击直播间的back_btn")
            time.sleep(self.action_short_dur)
            quit_room_text_view = self.d.xpath('//*[@text="退出房间"]')
            if self.retry_exists(quit_room_text_view, "quite_room_text_view"):
                log_print("点击退出房间")
                self.safe_click(quit_room_text_view, "quit_room_text_view")
                time.sleep(self.action_short_dur)
                direct_quit_btn = self.d.xpath('//*[@text="直接退出"]')
                direct_quit_btn.click_exists()
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未找到退出房间按钮")
                return False
        else:
            log_print("未找到返回按钮")
            return False
