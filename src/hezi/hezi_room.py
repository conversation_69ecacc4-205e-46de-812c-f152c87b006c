import time
from typing import List, <PERSON><PERSON>
from BaseMonkey import ActivityMonkey
from hezi.hezi_dialog import HeziDialog
from models.monkey_context import MonkeyContext
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from util.logger import log_print
from uiautomator2 import Device
from util.uiautomator_util import get_view_group_children_count

IS_TEST = True


# 赫兹直播间脚本


class HeziRoomMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        # 直播间Activity
        super().__init__(context, app_config, ".module_room.activity.RoomActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly
        # self.action_short_dur = 3
        # self.action_normal_dur = 6
        # self.action_long_dur = 10
        self.dialog_monkey = HeziDialog(context)
        self.send_publicly_result = False
        self.send_publicly_fail_reason = ""
        self.user_nick_list: List[str] = []
        self.send_privately_result_list: List[bool] = [False]
        self.send_privately_fail_reason_list: List[str] = [""]
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"room check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def check_activity_and_back(self, retry_count: int = 3, expand_fail: bool = True):
        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            elif activity == ".activity.HomeActivity":
                log_print(f"当前activity: {activity}，是home，终止任务")
                raise TerminatedMonkeyError(101, "页面是首页，终止直播间任务")
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_normal_dur}秒"
                log_print(error_msg)
                time.sleep(self.action_normal_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back,
                              None, None, retry_count, expand_fail)

    def on_hit_fail_action(self, fail_reason: str, count: int):
        if count > 5:
            log_print(f"连续命中失败原因{fail_reason}，终止直播间任务")
            raise TerminatedMonkeyError(101,
                                        f"连续命中失败原因{fail_reason}，终止直播间任务")
        return super().on_hit_fail_action(fail_reason, count)

    def run(self):
        try:
            self.run_room()
        except TerminatedMonkeyError as e:
            # 这里要区分home抛出的TerminatedMonkeyError create_process_finished异常
            if e.code == 101:
                log_print(f"{e.message}")
                return False
            else:
                raise e
        return True

    def run_room(self):
        # if IS_TEST:
        #     time.sleep(5)
        #     print("IS_TEST直播间完成")
        #     self.send_publicly_result = True
        #     self.send_privately_result_list = [True] * 1
        #     self.send_privately_fail_reason_list = [""] * 1
        #     self.total_process = 2
        #     self.do_process(2)
        #     return True

        d = self.d
        time.sleep(self.action_normal_dur)

        # 1. 公屏发送消息
        # send_publicly_result, send_publicly_fail_reason = self.chat_publicly(d)
        # self.send_publicly_result = send_publicly_result
        # self.send_publicly_fail_reason = send_publicly_fail_reason
        self.do_process(1)

        # 2. 私聊所有用户头像
        if self.is_template_normal_micro():
            log_print("模板是正常麦位，开始调用chat_all_users_privately2")
            self.chat_all_users_privately2(d)
        else:
            log_print("可能是特殊模板，调用chat_all_users_privately")
            self.chat_all_users_privately(d)

        log_print("直播间操作完成")
        if self.d.app_current()["activity"] == self.activity:
            log_print("当前activity是直播间，退出直播间")
            self.d.press("back")

        return True

    def chat_publicly(self, d: Device):
        """公屏发送消息"""
        send_result = False
        fail_reason = ""
        if not self.text_publicly:
            return send_result, fail_reason

        # 点击公屏发送入口
        chat_entry = d(resourceId="com.immomo.vchat:id/et_input_message")
        if self.retry_exists(chat_entry, "chat_entry"):
            self.safe_click(chat_entry, "chat_entry")
            time.sleep(self.action_short_dur)

            # 输入内容
            input_box = d(
                resourceId="com.immomo.vchat:id/et_real_input_message")
            if self.retry_exists(input_box, "input_box"):
                input_box.set_text(self.text_publicly)
                time.sleep(self.action_short_dur)

                # 点击发送
                send_btn = d(resourceId="com.immomo.vchat:id/tv_room_send")
                if self.retry_exists(send_btn, "send_btn"):
                    self.safe_click(send_btn, "公屏发送按钮点击")
                    send_result = True
                    log_print("公屏消息发送成功")
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"

        return send_result, fail_reason

    def is_template_normal_micro(self):
        rv_micro_list1 = self.d(
            resourceId="com.immomo.vchat:id/room_member_on_mic_recycler_view")
        rv_micro_list2 = self.d(
            resourceId="com.immomo.vchat:id/room_accompany_member_on_mic_recycler_view")
        return self.retry_when_false(lambda: rv_micro_list1.exists or rv_micro_list2.exists, "检测模板是否是正常麦位")

    def chat_all_users_privately2(self, d: Device):
        template_type = 1
        rv_micro_list = d(
            resourceId="com.immomo.vchat:id/room_member_on_mic_recycler_view")
        if not rv_micro_list.exists:
            template_type = 2
            rv_micro_list = d(
                resourceId="com.immomo.vchat:id/room_accompany_member_on_mic_recycler_view")

        if not rv_micro_list.exists:
            log_print("正常模板:未找到用户头像容器")
            return

        user_count = get_view_group_children_count(rv_micro_list)

        log_print(f"正常模板:模板类型：{template_type}，找到 {user_count} 个连麦坑位")
        self._total_process = user_count + 1  # +1是公屏
        self.send_privately_result_list = [False] * user_count
        self.send_privately_fail_reason_list = ["ignore"] * user_count

        for i in range(user_count):
            item = rv_micro_list.child(index=i)
            user_nick_view = item.child(
                resourceId="com.immomo.vchat:id/on_mic_user_name")
            if self.retry_exists(user_nick_view, f"user_nick_view: {i}"):
                nick_name = user_nick_view.get_text()
                log_print(f"检测第{i}坑位用户名：{nick_name}")
            else:
                log_print(f"检测第{i}坑位没有user_nick_view")

        for i in range(user_count):
            item = rv_micro_list.child(index=i)
            if not self.retry_exists(item, f"正常模板:item: {i}是否存在"):
                self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                continue
            user_nick_view = item.child(
                resourceId="com.immomo.vchat:id/on_mic_user_name")

            if self.retry_exists(user_nick_view, f"user_nick_view: {i}"):
                # 这里获取用户名有BUG，出现坑位对应的昵称不准确，比如2号坑位或取到1号坑位的昵称
                # 这会导致一个问题，如果1号坑位是正常的昵称，2号坑位是“号麦位”，点击这个没上麦的“号麦位”会弹出录音权限弹窗，挡住流程
                nick_name = user_nick_view.get_text()
                if nick_name in self.user_nick_list:
                    log_print(f"正常模板:第{i + 1}个用户昵称已存在，跳过")
                    self.do_process(i + 1 + 1)
                    continue
                self.user_nick_list.append(nick_name)
                log_print(f"正常模板:找到第{i + 1}个用户，昵称：{nick_name}")
                if "号麦位" in nick_name:
                    log_print(f"正常模板:第{i + 1}个坑位没上麦，跳过")
                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                    continue
                avatar_container = item.child(
                    resourceId="com.immomo.vchat:id/on_mic_user_avatar_iv")
                if not self.retry_exists(avatar_container, f"avatar: {i}"):
                    log_print(f"正常模板:第{i + 1}个用户头像不存在，跳过")
                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                    continue

                try:
                    log_print(f"正常模板:点击第 {i + 1} 个用户头像")
                    self.safe_click(avatar_container, "点击avatar")
                    log_print(f"正常模板:点击第 {i + 1} 个用户头像成功")
                    time.sleep(self.action_short_dur)

                    if self.dialog_monkey.close_micro_permission_dialog():
                        log_print(
                            f"检测到录音权限弹窗，并且处理了弹窗，这是第 {i + 1} 个用户头像，昵称：{nick_name}，跳过")
                        self.do_process(i + 1 + 1)
                        continue

                    # 执行私聊步骤
                    send_privately_result, send_privately_fail_reason = self.chat_single_user_privately(
                        d)
                    self.send_privately_result_list[i] = send_privately_result
                    self.send_privately_fail_reason_list[i] = send_privately_fail_reason

                    log_print(f"正常模板:私聊第 {i + 1} 个用户成功，关闭卡片弹窗")

                    # 返回直播间
                    self.d.press("back")
                    time.sleep(self.action_short_dur)
                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏

                except Exception as e:
                    if isinstance(e, TerminatedMonkeyError):
                        raise
                    else:
                        log_print(f"正常模板:处理第 {i + 1} 个头像时出错: {str(e)}")
                        import traceback
                        traceback.print_exc()
                        self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                        continue

    def chat_all_users_privately(self, d):
        """私聊所有用户头像"""
        # 查找所有用户头像
        avatar_containers = d(
            resourceId="com.immomo.vchat:id/on_mic_user_avatar_iv")

        if not self.retry_exists(avatar_containers, "avatar_containers"):
            log_print("特殊模板:未找到用户头像容器")
            return

        # 获取所有头像的数量
        avatar_count = avatar_containers.count
        log_print(f"特殊模板:找到 {avatar_count} 个用户头像")
        self._total_process = avatar_count + 1  # +1是公屏
        self.send_privately_result_list = [False] * avatar_count
        self.send_privately_fail_reason_list = [""] * avatar_count

        # 遍历所有头像
        for i in range(avatar_count):
            try:
                # 获取第i个头像
                avatar = avatar_containers[i]
                if self.retry_exists(avatar, "avatar"):
                    log_print(f"特殊模板:点击第 {i + 1} 个用户头像")
                    self.safe_click(avatar, "avatar")
                    log_print(f"特殊模板:点击第 {i + 1} 个用户头像成功")
                    time.sleep(self.action_normal_dur)

                    # 执行私聊步骤
                    send_privately_result, send_privately_fail_reason = self.chat_single_user_privately(
                        d)
                    self.send_privately_result_list[i] = send_privately_result
                    self.send_privately_fail_reason_list[i] = send_privately_fail_reason

                    log_print(f"特殊模板:私聊第 {i + 1} 个用户成功，关闭卡片弹窗")
                    # 返回直播间
                    d.press("back")
                    time.sleep(self.action_short_dur)

                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                else:
                    log_print(f"特殊模板:第 {i + 1} 个头像不存在")
                    break
            except Exception as e:
                if isinstance(e, TerminatedMonkeyError):
                    raise
                else:
                    log_print(f"特殊模板:处理第 {i + 1} 个头像时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue

    def chat_single_user_privately(self, d: Device):
        """对单个用户执行私聊步骤"""
        send_result = False
        fail_reason = ""
        # 点击发消息按钮
        message_btn = d(resourceId="com.immomo.vchat:id/message_status")
        if self.retry_exists(message_btn, "message_btn是否存在"):
            log_print(f"单聊 点击发消息按钮")
            self.safe_click(message_btn, "message_btn点击")
            log_print(f"单聊 点击发消息按钮成功")
            time.sleep(self.action_normal_dur)

            # 检查是否已发送过相同文案
            chat_rv = d(resourceId="com.immomo.vchat:id/xrv_chat_list")
            has_sent = False
            if self.retry_exists(chat_rv, "chat_rv"):
                idx = 0
                while True:
                    item = chat_rv.child(index=idx)
                    if not item.exists:
                        break
                    textview = item.child(
                        resourceId="com.immomo.vchat:id/tv_chat_content_self")
                    if textview.exists and textview.get_text() == self.text:
                        log_print("已发送过相同私聊文案")
                        has_sent = True
                        break
                    idx += 1

            if not has_sent:
                # 输入私聊文案
                input_box = d(
                    resourceId="com.immomo.vchat:id/et_input_message")

                if self.retry_when_false(lambda: input_box.exists):
                    input_box.set_text(self.text)
                    time.sleep(self.action_short_dur)
                    send_btn = d(resourceId="com.immomo.vchat:id/tv_room_send")
                    if self.retry_when_false(lambda: send_btn.exists):
                        self.safe_click(send_btn, "私聊发送按钮点击")
                        log_print("私聊消息发送成功")
                        time.sleep(self.action_short_dur)

                        # 回退收起键盘
                        d.press("back")
                        time.sleep(self.action_short_dur)

                        # 检查是否发送成功
                        chat_rv = d(
                            resourceId="com.immomo.vchat:id/xrv_chat_list")
                        sent = False
                        if chat_rv.exists:
                            idx = 0
                            while True:
                                item = chat_rv.child(index=idx)
                                if not item.exists:
                                    break
                                textview = item.child(
                                    resourceId="com.immomo.vchat:id/tv_chat_content_self")
                                if textview.exists and textview.get_text() == self.text:
                                    sent = True
                                    break
                                idx += 1

                        if sent:
                            log_print("私聊文案已成功发送")
                            send_result = True
                        else:
                            log_print("未检测到私聊文案发送成功")
                            fail_reason = "未检测到私聊文案发送成功"
                    else:
                        log_print("未找到私聊发送按钮")
                        fail_reason = "未找到私聊发送按钮"
                else:
                    log_print("未找到私聊输入框")
                    fail_reason = "未找到私聊输入框"
            else:
                log_print("已经发送过相同私聊文案")
                fail_reason = "已经发送过相同私聊文案"

            # 关闭聊天窗口
            close_btn = d(resourceId="com.immomo.vchat:id/iv_common_back")
            if close_btn.exists:
                close_btn.click()
                time.sleep(self.action_short_dur)
        else:
            log_print("未找到发消息按钮")
            fail_reason = "未找到发消息按钮"

        return send_result, fail_reason
