import time
from BaseMonkey import ActivityMonkey
from hezi.hezi_dialog import Hezi<PERSON><PERSON><PERSON>
from hezi.hezi_quit_room import Hezi<PERSON><PERSON>RoomMonkey
from hezi.hezi_room import Hezi<PERSON>oomMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import UiObject
from typing import Union, Tuple


# 赫兹首页脚本


class HeziHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config, room_monkey: ActivityMonkey):
        # 首页Activity
        super().__init__(context, app_config, ".activity.HomeActivity")
        self.room_monkey = room_monkey
        self.dialog_monkey = HeziDialog(context)
        self.live_room_list = []
        self.fail = 0
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"home check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def check_activity_and_back(self, retry_count: int = 4, expand_fail: bool = True):
        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            elif activity == ".module_room.activity.RoomActivity":
                log_print(f"当前activity: {activity}，是Room，点击退出房间")
                result = HeziQuitRoomMonkey(self.context).run()
                if result:
                    log_print("退出房间成功")
                    return True, "退出房间成功"
                else:
                    log_print("退出房间失败")
                    return False, "退出房间失败"
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_long_dur}秒"
                log_print(f"{error_msg}")
                time.sleep(self.action_long_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back, None, None, retry_count, expand_fail)

    def run(self):
        try:
            self.run_home()
        except TerminatedMonkeyError as e:
            if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                log_print("收到完成信号，赫兹任务已完成")
            else:
                raise e

        return True

    def run_home(self):
        d = self.d
        # 1. 切换到底部聊天室tab
        chat_tab = d.xpath(
            '(//*[@resource-id="com.immomo.vchat:id/cfl_home_tab"])[2]')
        log_print(f"开始运行{self.app_config.name} home")
        if self.retry_exists(chat_tab, "chat_tab"):
            log_print("点击底部聊天室tab")
            self.safe_click(chat_tab, "chat_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到聊天室tab，退出")
            return
        log_print(f"点击底部聊天室tab成功")
        # 不知道为什么会自动进入直播间，所以先检测一下activity
        time.sleep(self.action_normal_dur)
        # self.check_activity_and_back()

        # 2. 切换到"全部"一级tab
        all_tab = d.xpath('//*[@text="全部"]')

        if self.retry_exists(all_tab, "all_tab"):
            log_print("点击全部一级tab")
            self.safe_click(all_tab, "all_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到全部一级tab，退出")
            return

        # 3. 切换到"发现"二级tab
        discover_tab = d.xpath('//*[@text="发现"]')
        if self.retry_exists(discover_tab, "discover_tab"):
            log_print("点击发现二级tab")
            self.safe_click(discover_tab, "discover_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到发现二级tab，退出")
            return

        self.process_room_list()

        if self._fail_reason_dict:
            log_print("\n失败原因统计：")
            for reason, count in self._fail_reason_dict.items():
                log_print(f"{reason}: {count} 次")
        else:
            log_print("无失败原因，全部成功！")

        return True

    def process_room_list(self):
        while True:
            if self._process >= self.app_config.count:
                log_print("已完成进入次数..")
                break

            self.dialog_monkey.try_dismiss()

            time.sleep(self.action_normal_dur)
            item_containers = self.d(
                resourceId="com.immomo.vchat:id/item_layout")
            if not self.retry_exists(item_containers, "item_containers"):
                log_print("未找到聊天室列表 process_room_list item失败，尝试滑动")
                self.switch_live_room(self.d)
                continue

            item_count = item_containers.count
            if item_count == 0:
                log_print("未找到聊天室列表 item_containers空，尝试滑动")
                self.switch_live_room(self.d)
                continue

            log_print(f"找到{item_count}个聊天室")

            for i in range(item_count):
                if self._process >= self.app_config.count:
                    log_print("已完成进入次数..")
                    break

                item = item_containers[i]
                try:
                    find_result = self.find_room(item, i)
                    if not find_result:
                        break
                except Exception as e:
                    log_print(f"处理第 {i + 1} 个聊天室时出错: {str(e)}")
                    self.check_activity_and_back()
                    continue

            self.switch_live_room(self.d)

    def find_room(self, item: UiObject, i: int) -> Union[bool, None]:
        topic_name_view = item.child(
            resourceId="com.immomo.vchat:id/tv_room_topic")
        if self.retry_exists(topic_name_view, "topic_name_view", retry_count=3):
            topic_name = topic_name_view.get_text()
            log_print(f"找到第{i + 1}个聊天室，主题：{topic_name}")
            # 判断topic_name是否在live_room_list中
            if topic_name in self.live_room_list:
                log_print(f"第{i + 1}个聊天室主题：{topic_name} 已存在，跳过")
                return False
            self.live_room_list.append(topic_name)
            self.safe_click(topic_name_view, "topic_name_view")
            log_print(f"点击聊天室：{topic_name}")
            time.sleep(self.action_normal_dur)
            self.dialog_monkey.try_dismiss()
            room_monkey = HeziRoomMonkey(self.context, self.app_config)
            time.sleep(self.action_normal_dur)

            def progress_callback(progress: int, total_process: int):
                log_print(f"进度: {progress}/{total_process}")
                if progress <= 1:
                    return
                result_list = room_monkey.send_privately_result_list
                fail_reason_list = room_monkey.send_privately_fail_reason_list
                real_index = progress - 1 - 1
                send_result = result_list[real_index]
                fail_reason = fail_reason_list[real_index]
                if fail_reason == "ignore":
                    return
                self.do_process(self._process + 1)
                if send_result:
                    self._success_count += 1
                else:
                    self.fail += 1
                    if fail_reason:
                        if fail_reason in self._fail_reason_dict:
                            self._fail_reason_dict[fail_reason] += 1
                        else:
                            self._fail_reason_dict[fail_reason] = 1
                log_print(
                    f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {self.fail}, 失败原因: {fail_reason}")
                if self._process >= self.app_config.count:
                    log_print("已完成进入次数..")
                    raise TerminatedMonkeyError.create_process_finished()

            if self.d.app_current()["activity"] == self.room_monkey.activity:
                room_monkey.register_progress_callback(progress_callback)
                room_monkey.run()
                room_monkey.unregister_progress_callback(progress_callback)
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未能正常进入直播间，再次尝试关闭弹窗")
                time.sleep(self.action_long_dur)
                self.dialog_monkey.try_dismiss()
                time.sleep(self.action_normal_dur)
                if self.d.app_current()["activity"] == self.room_monkey.activity:
                    room_monkey.register_progress_callback(progress_callback)
                    room_monkey.run()
                    room_monkey.unregister_progress_callback(progress_callback)
                    time.sleep(self.action_normal_dur)
                    return True
                else:
                    log_print("未能正常进入直播间，失败")
                    return False
        else:
            log_print(f"未找到第{i + 1}个聊天室")
            return False

    def has_more_tofu(self):
        """检查是否还有更多豆腐块可点击"""
        rv = self.d(resourceId="com.immomo.vchat:id/rlv_home_room")
        if rv.exists:
            tofu_items = rv.child(resourceId="com.immomo.vchat:id/item_layout")
            return tofu_items.exists
        return False

    def switch_live_room(self, d):
        """向上滑动切换直播间列表"""
        width, height = d.window_size()
        for attempt in range(3):
            x = width * (0.5 + 0.01 * attempt)
            y_start = height * (0.8 - 0.02 * attempt)
            y_end = height * (0.3 + 0.02 * attempt)
            log_print(
                f"向上滑动切换直播间列表 attempt:{attempt + 1} from:{x},{y_start} to:{x},{y_end}")
            d.swipe(x, y_start, x, y_end, 0.1)
            time.sleep(self.action_normal_dur)

            # 检查是否有新的豆腐块
            if self.has_more_tofu():
                log_print("滑动后找到新的豆腐块")
                break
        else:
            log_print("多次滑动后仍未找到新的豆腐块")
