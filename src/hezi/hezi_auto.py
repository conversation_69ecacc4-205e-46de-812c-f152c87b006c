from BaseMonkey import A<PERSON><PERSON>onkey
from monkey_config import AppMonkeyConfig
from hezi.hezi_home import He<PERSON><PERSON><PERSON>Monkey
from hezi.hezi_room import <PERSON><PERSON><PERSON><PERSON><PERSON>onkey
from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext

# 赫兹 App 自动灌水脚本


class HeziAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = HeziRoomMonkey(context, app_config)
        self.home_monkey = HeziHomeMonkey(
            context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self) -> str:
        return "com.immomo.vchat"

    def run(self):
        # 执行首页逻辑
        self.home_monkey.run()
        log_print("赫兹自动私聊任务完成！")
