# -*- coding: utf-8 -*-
import time

from BaseMonkey import ActivityMonkey
from taqu.taqu_dialog import TaquDialog
from util.logger import log_print
from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext


class TaquIMMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        # Im Activity - 需要根据实际的他趣应用Activity修改
        super().__init__(context, app_config, ".component.chat.ConversationActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly or app_config.text
        self.dialog_monkey = TaquDialog(context)
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"room check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)

    def run(self):
        log_print("start to process im......")
        return self.start_send_text(self.d)
        
    def check_free_chat(self, d) -> bool:
        return d(textContains="送礼开聊").exists
    
    def check_forbid_chat(self, d) -> bool:
        return d(textContains="今日自闭中，请不要打扰我").exists
    
    def start_send_text(self, d):
        time.sleep(self.action_short_dur)
        send_result = False
        fail_reason = "当前 activity 不是 IM "
        if d.app_current()["activity"] == ".component.chat.ConversationActivity":
            if self.check_free_chat(d):
                fail_reason = "需要付费私聊！"
            elif self.check_forbid_chat(d):
                fail_reason = "设置禁止私聊"
            else:
                log_print("准备发送私聊")
                return self.send_text_to(d)
        log_print(fail_reason)
        closeBtn = d(text="先不聊了")
        if closeBtn.exists:
            closeBtn.click()
        else:
            self.close_im_wind(d)
        return send_result, fail_reason
    
    def send_text_to(self, d):
        send_result = False
        fail_reason = ""
         # 输入私聊文案
        input_box = d(resourceId="com.taqu.app:id/et_sendmessage")
        
        if self.retry_when_false(lambda: input_box.exists):
            input_box.click()
            input_box.set_text(self.text)
            time.sleep(self.action_short_dur)
            send_btn = d(resourceId ="com.taqu.app:id/btn_send")
            if self.retry_when_false(lambda: send_btn.exists):
                send_btn.click()
                log_print("私聊消息发送成功")
                time.sleep(self.action_short_dur)
                # 检查是否发送成功
                # chat_rv = d(resourceId="com.taqu.app:id/list_conversation")
                # item_containers = self.d(resourceId="com.taqu.app:id/tvTitle") #com.taqu.app:id/recyclerView
                chat_rv = d(resourceId="com.taqu.app:id/item_root")
                log_print( f"消息个数：{chat_rv.count}" )
                
                sent = False
                if chat_rv.exists:
                    idx = 0
                    while True:
                        item = chat_rv.child(index=idx)
                        if not item.exists:
                            break
                        # textview = item.child(resourceId="com.immomo.vchat:id/tv_chat_content_self")
                        # send_text = item.child_by_text(self.text)
                        textview = item.child(resourceId="com.taqu.app:id/content_text")
                        # log_print("获取的消息文案：", textview.get_text())
                        if textview.exists and textview.get_text() == self.text:
                            sent = True
                            break

                        # log_print(send_text) #fizzctodo maybe last time send
                        # if textview.exists and textview.get_text() == self.text:
                        # if send_text is not None:
                        #     sent = True
                        #     break
                        idx += 1
                
                if sent:
                    log_print("私聊文案已成功发送")
                    send_result = True
                else:
                    log_print("未检测到私聊文案发送成功")
                    fail_reason = "未检测到私聊文案发送成功"
            else:
                log_print("未找到私聊发送按钮")
                fail_reason = "未找到私聊发送按钮"                    
        else:
            log_print("未找到私聊输入框")
            fail_reason = "未找到私聊输入框"
            
        self.close_im_wind(d)
    
        return send_result, fail_reason
        
    def close_im_wind(self, d):
        log_print("关闭私聊")
        backBtn = d(resourceId="com.taqu.app:id/item_left_back")
        if backBtn.exists:
            backBtn.click()
        else:
            # 第一次back是为了关闭软键盘.
            d.press("back")
            time.sleep(1.5)
            # 判断一下是否还在IM页面,如果在的话在back一次.
            if d.app_current()["activity"] == ".component.chat.ConversationActivity":
                d.press("back")
