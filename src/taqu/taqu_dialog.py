# -*- coding: utf-8 -*-
import time

from BaseMonkey import BaseDialog
from util.logger import log_print


class TaquDialog(BaseDialog):
    def __init__(self, context):
        super().__init__(context)

    def close_notification_tip_dialog(self):
        """通知栏弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="开启消息通知"]')
        close_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/ivClose")
        if tmp_dialog.exists and close_btn.exists:
            log_print("通知栏弹窗，点击取消")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_hello_tip_dialog(self):
        """用户推荐弹窗"""

        dialog_bottom = self.d(resourceId="com.xingjiabi.shengsheng:id/llBottomBtn")
        reply_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/btnReplyImmediately")
        close_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/tvIgnore")
        if dialog_bottom.exists and reply_btn.exists and close_btn.exists:
            log_print("用户推荐弹窗，点击取消")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_remain_dialog(self):
        """恢复弹窗"""
        remain_dialog = self.d.xpath('//*[@text="上次是不是意外退出呢？继续回来聊聊吧"]')
        close_dialog = self.d.xpath('//*[@text="取消"]')
        if remain_dialog.exists and close_dialog.exists:
            log_print(f"恢复上次弹窗，点击取消")
            close_dialog.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_exit_entry_dialog(self):
        """退出进入弹窗"""
        config_exit_dialog = self.d.xpath('//*[@text="需要退出畅聊圈才能前往其他畅聊圈"]')
        close_btn = self.d.xpath('//*[@text="退出并前往"]')
        if config_exit_dialog.exists and close_btn.exists:
            log_print("退出进入聊天弹窗，点击允许")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_invate_micro_dialog(self):
        """上麦弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="邀请你上麦一起聊天"]')
        close_btn = self.d.xpath('//*[@text="残忍拒绝"]')
        if tmp_dialog.exists and close_btn.exists:
            log_print("上麦弹窗，点击取消")
            close_btn.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_room_exit_dialog(self):
        """房间退出弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="下次可以在这里找到哦"]')
        close_btn = self.d.xpath('//*[@text="直接退出"]')
        if tmp_dialog.exists and close_btn.exists:
            log_print("退出房间提示弹窗，点击确定")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_reCharge_dialog(self):
        """充值弹窗"""
        top_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/ivTopIcon")
        content_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/clContentView")
        close_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/ivClose")
        if close_btn.exists and top_btn.exists and content_btn.exists:
            log_print("充值弹窗，点击关闭")
            close_btn.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_newinvite_dialog(self):
        """新邀请弹窗"""
        top_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/tvTitle")
        content_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/tvSubTitle")
        close_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/tvConfirm")
        if close_btn.exists and top_btn.exists and content_btn.exists:
            log_print("新邀请弹窗，点击关闭")
            close_btn.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_notification_tip_dialog():
            result = True
        if self.close_hello_tip_dialog():
            result = True
        if self.close_remain_dialog():
            result = True
        if self.close_exit_entry_dialog():
            result = True
        if self.close_invate_micro_dialog():
            result = True
        if self.close_room_exit_dialog():
            result = True
        if self.close_reCharge_dialog():
            result = True
        if self.close_newinvite_dialog():
            result = True
        return result
