# -*- coding: utf-8 -*-
import time
import random
from typing import List
from BaseMonkey import ActivityMonkey
from taqu.taqu_dialog import TaquDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import Device
from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext



# 他趣直播间脚本
class TaquRoomMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        # 直播间Activity - 需要根据实际的他趣应用Activity修改
        super().__init__(context, app_config, "com.xmhaibao.hichat.activity.HiChatRoomActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly or app_config.text
        self.dialog_monkey = TaquDialog(context)
        self.send_publicly_result = False
        self.send_publicly_fail_reason = ""
        self.send_privately_result_list: List[bool] = [False]
        self.send_privately_fail_reason_list: List[str] = [""]
        self.retry_action = self.check_when_retry
        self.hello_array = ["哈喽，", "你好，", "hello，", "Hi，", "嗨，"]

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"room check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)

    def run(self):
        log_print("**** 进入聊天室 ****")

        d = self.d
        self.dialog_monkey.try_dismiss()
        d.press("back") #有些特别弹窗需要先点击返回
        time.sleep(self.action_normal_dur)
        self.do_process(1)


        send_publicly_result, send_publicly_fail_reason = self.chat_publicly(d)
        self.send_publicly_result = send_publicly_result
        self.send_publicly_fail_reason = send_publicly_fail_reason

        # 1. 私聊所有用户头像
        self.chat_all_users_privately(d)

        send_publicly_result, send_publicly_fail_reason = self.chat_publicly(d)
        self.send_publicly_result = send_publicly_result
        self.send_publicly_fail_reason = send_publicly_fail_reason

        cur_activity = self.d.app_current()["activity"]
        log_print(f"直播间操作完成 cur_activity:{cur_activity}")
        time.sleep(self.action_normal_dur)
        if self.d.app_current()["activity"] == self.activity or self.d.app_current()["activity"] != ".app.NavigationActivity":
            log_print("当前 activity 不是 home，返回")
            self.d.press("back")
            close_btn = self.d(resourceId="com.xingjiabi.shengsheng:id/ivCloseRoom")
            if self.retry_exists(close_btn, "close_liveroom", 4):
                self.safe_click(close_btn, "close_btn")
                time.sleep(self.action_short_dur)
                self.dialog_monkey.close_room_exit_dialog()
                log_print("点击关闭直播间按钮")
            else:
                self.d.press("back")
        return True


    def chat_publicly(self, d):
        """公屏发送消息"""
        send_result = False
        fail_reason = ""
        # 点击公屏发送入口
        chat_entry = d(resourceId="com.xingjiabi.shengsheng:id/tvInputHint")
        if self.retry_exists(chat_entry, "chat_entry"):
            self.safe_click(chat_entry, "chat_entry")
            time.sleep(self.action_short_dur)
            # 输入内容
            input_box = d(resourceId="com.xingjiabi.shengsheng:id/etContent")
            if self.retry_exists(input_box, "input_box"):
                input_box.set_text(self.text_publicly)
                time.sleep(self.action_short_dur)

                # 点击发送
                send_btn = d(resourceId="com.xingjiabi.shengsheng:id/tvSend")
                if self.retry_exists(send_btn, "send_btn"):
                    self.safe_click(send_btn, "send_btn")
                    send_result = True
                    log_print("公屏消息发送成功")
                    d.press("back")
                    d.press("back") #需要两次返回
                    time.sleep(self.action_short_dur)

                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"

        return send_result, fail_reason


    def chat_all_users_privately(self, d):
        """私聊所有用户头像"""
        # 查找所有用户头像
        online_user_containers = d(resourceId="com.xingjiabi.shengsheng:id/rvOnlineUserList")

        if not self.retry_exists(online_user_containers, "online_user_containers"):
            log_print("未找到在线用户头像容器")
            return
        avatar_containers = online_user_containers.child(resourceId="com.xingjiabi.shengsheng:id/onlineUserView")
        # 获取所有头像的数量
        avatar_count = avatar_containers.count
        log_print(f"找到 {avatar_count} 个用户头像")
        self._total_process = avatar_count + 1  # +1是公屏
        self.send_privately_result_list = [False] * avatar_count
        self.send_privately_fail_reason_list = ["ignore"] * avatar_count
        # 遍历所有头像
        for i in range(avatar_count):
            try:
                # 获取第i个头像
                avatar = avatar_containers[i]
                if self.retry_exists(avatar, "user_avatar", 3):
                    log_print(f"点击第 {i+1} 个用户头像")
                    self.safe_click(avatar, "avatar")
                    log_print(f"点击第 {i+1} 个用户头像成功")
                    time.sleep(self.action_normal_dur)

                    user_nick_view = self.d(resourceId="com.xingjiabi.shengsheng:id/tvNickName")
                    if self.retry_exists(user_nick_view, "user_nick_view", 3):
                        nick_name = user_nick_view.get_text()
                        log_print(f"找到第{i + 1}个用户头像的昵称：{nick_name}")
                    else:
                        nick_name = "Unknown"
                        log_print(f"找不到第{i + 1}个用户头像的昵称！")

                    if self.follow(nick_name):
                        self.safe_click(avatar, "avatar")
                        time.sleep(self.action_short_dur)
                    # 执行私聊步骤
                    send_privately_result, send_privately_fail_reason = self.chat_single_user_privately(d)
                    self.send_privately_result_list[i] = send_privately_result
                    self.send_privately_fail_reason_list[i] = send_privately_fail_reason

                    log_print(f"私聊第 {i+1} 个用户成功，关闭卡片弹窗")
                    if send_privately_fail_reason == "ignoreUser":
                        continue
                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                    # 返回直播间
                    time.sleep(3)

                else:
                    log_print(f"第 {i+1} 个头像不存在")
                    break
            except Exception as e:
                if isinstance(e, TerminatedMonkeyError):
                    raise
                else:
                    log_print(f"处理第 {i+1} 个头像时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    self.dialog_monkey.try_dismiss()
                    continue


    def follow(self, nick_name: str) -> bool:
        follow_view = self.d(resourceId="com.xingjiabi.shengsheng:id/tvFollow")
        if self.retry_exists(follow_view, "查找关注按钮", 3):
            follow_status = follow_view.child(index=0).get_text()
            log_print(f"关注按钮的文案：{follow_status}")
            log_print(f"找到关注按钮，点击关注 {nick_name}")
            follow_view.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            log_print(f"找不到关注按钮或者已经关注 {nick_name}")

        return False


    def close_im_wind(self, d):
        log_print("关闭私聊弹窗")
        d.press("back")
        time.sleep(1.5)

    def chat_single_user_privately(self, d: Device):
        """对单个用户执行私聊步骤"""
        send_result = False
        fail_reason = ""
        # 点击发IM消息按钮
        message_btn = d(resourceId="com.xingjiabi.shengsheng:id/ivComment")
        if self.retry_exists(message_btn, "message_btn"):
            log_print(f"单聊 点击发消息按钮")
            self.safe_click(message_btn, "message_btn")
            log_print(f"单聊 点击发消息按钮成功")
            time.sleep(self.action_normal_dur)

            # 检查是否已发送过相同文案
            chat_container = d(resourceId="com.xingjiabi.shengsheng:id/recyclerViewMsg")
            if self.retry_exists(chat_container, "chat_container", 2):
                rightMsg = chat_container.child(resourceId="com.xingjiabi.shengsheng:id/relRightCommon")
                if self.retry_exists(rightMsg, "rightMsg", 2):
                    log_print("已发送过相同私聊文案")
                    self.close_im_wind(d)
                    return send_result, "ignoreUser"

            has_sent = False
            if not has_sent:
                # 输入私聊文案
                input_box = d(resourceId="com.xingjiabi.shengsheng:id/editContent")
                nicknametext = d(resourceId="com.xingjiabi.shengsheng:id/tvNickname")
                if self.retry_when_false(lambda: input_box.exists):
                    log_print("点击私聊输入框")
                    input_box.click()
                    time.sleep(self.action_short_dur)
                    self.dialog_monkey.try_dismiss()
                    prefix = ""
                    if nicknametext.exists:
                        nickname = nicknametext.get_text()
                        if nickname:
                            prefix = random.choice(self.hello_array) + nickname + ""
                    input_box.set_text(f"{prefix}，" + self.text)
                    time.sleep(self.action_short_dur)
                    log_print(input_box.get_text())
                    send_btn = d(resourceId="com.xingjiabi.shengsheng:id/tvSend")
                    if self.retry_when_false(lambda: send_btn.exists):
                        self.safe_click(send_btn, "私聊发送按钮点击")
                        log_print("点击私聊发送按钮成功")
                        d.press("back") # 回退收起键盘
                        time.sleep(self.action_short_dur)
                        self.dialog_monkey.try_dismiss()
                        # 检查是否发送成功
                        chat_rv = d(resourceId="com.xingjiabi.shengsheng:id/recyclerViewMsg")
                        message_list = chat_rv.child(resourceId="com.xingjiabi.shengsheng:id/relRightCommon")
                        message_count = message_list.count
                        log_print(f"找到 发送的聊天记录条 {message_count}")
                        sent = False
                        if message_count > 0:
                            lastMsg = message_list[message_count - 1]
                            lastStauts = lastMsg.child(resourceId="com.xingjiabi.shengsheng:id/tvRestSend")
                            # 判断是否有重发机制
                            if not lastStauts.exists:
                                sent = True
                        if sent:
                            log_print("私聊文案已成功发送")
                            send_result = True
                        else:
                            log_print("未检测到私聊文案发送成功标志")
                            fail_reason = "未检测到私聊文案发送成功标志"
                    else:
                        log_print("未找到私聊发送按钮")
                        fail_reason = "未找到私聊发送按钮"
                else:
                    log_print("未找到私聊输入框")
                    fail_reason = "未找到私聊输入框"
            else:
                log_print("已经发送过相同私聊文案")
                fail_reason = "已经发送过相同私聊文案"

            # 关闭聊天窗口
            close_btn = d(resourceId="com.xingjiabi.shengsheng:id/btnMessageChatBack")
            if close_btn.exists:
                close_btn.click()
                log_print("点击关闭私聊弹窗按钮")
                time.sleep(self.action_short_dur)
            else:
                self.close_im_wind(d)
        else:
            log_print("未找到发消息按钮, 可能是自己本身")
            fail_reason = "未找到发消息按钮"

        return send_result, fail_reason
