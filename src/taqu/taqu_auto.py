# -*- coding: utf-8 -*-
import time
from BaseMonkey import AppMonkey
from monkey_config import AppMonkeyConfig
from taqu.taqu_home import TaquH<PERSON>Monkey
from taqu.taqu_room import Taqu<PERSON><PERSON><PERSON>onkey

from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext


# 他趣 App 自动灌水脚本
class TaquAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = TaquRoomMonkey(context, app_config)
        self.home_monkey = TaquHomeMonkey(context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self) -> str:
        return "com.xingjiabi.shengsheng"  # 需要根据实际的他趣包名修改

    def run(self):
        # 执行首页逻辑
        self.home_monkey.run()
        
        log_print("他趣自动任务完成！")
