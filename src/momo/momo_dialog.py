import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


class MomoDialog(BaseDialog):

    def close_web_dialog(self):
        web_dialog = self.d(
            resourceId="com.immomo.momo:id/mk_dialog_webview_container")
        if web_dialog.exists:
            log_print("找到web弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_young_mode(self):
        young_mode = self.d.xpath('//*[@text="青少年模式"]')
        if young_mode.exists:
            log_print("找到青少年模式弹窗，点击我知道了")
            young_mode_known = self.d.xpath('//*[@text="我知道了"]')
            if young_mode_known.exists:
                young_mode_known.click()
                time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # 在线匹对弹窗
    def close_match(self):
        match = self.d.xpath('//*[@text="找到一个在线配对"]')
        if match.exists:
            log_print("找到在线匹对弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def process_account_abnormal_dialog(self):
        account_abnormal_dialog = self.d.xpath('//*[@text="账号功能受限，无法观看直播"]')
        if account_abnormal_dialog.exists:
            log_print("找到账号功能受限")
            raise TerminatedMonkeyError.create_terminated_app_run(
                "账号异常-功能受限")
        else:
            return False

    def process_login_activity(self):
        activity = self.d.app_current()["activity"]
        if activity == "com.immomo.android.mvvm.luaview.view.LoginLuaActivity":
            log_print("当前为登录页面")
            raise TerminatedMonkeyError.create_terminated_app_run(
                "账号异常-未登录")
        else:
            return False


    def try_dismiss(self):
        result = False
        if self.close_web_dialog():
            result = True
        if self.close_young_mode():
            result = True
        if self.close_match():
            result = True
        if self.process_account_abnormal_dialog():
            result = True
        if self.process_login_activity():
            result = True
        return result
