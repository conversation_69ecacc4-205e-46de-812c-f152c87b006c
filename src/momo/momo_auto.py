import time
from BaseMonkey import AppMonkey
from models.monkey_context import <PERSON><PERSON><PERSON>x<PERSON>
from momo.home_monkey import MomoH<PERSON>Monkey
from momo.room_monkey import Momo<PERSON><PERSON>Monkey
from monkey_config import AppMonkeyConfig
from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print

# 陌陌自动脚本


class MomoAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = MomoRoomMonkey(context, app_config)
        self.home_monkey = MomoHomeMonkey(
            context, app_config, self.room_monkey)
        self.proxy_monkey = self.room_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self):
        return "com.immomo.momo"

    def run(self):
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            else:
                log_print(f"等待陌陌对应的Activity，当前Activity: {activity}")
                time.sleep(self.action_normal_dur)
