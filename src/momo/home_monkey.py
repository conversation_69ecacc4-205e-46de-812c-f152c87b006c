import time
from BaseMonkey import ActivityMonkey
from momo.momo_dialog import MomoDialog
from util.logger import log_print


class MomoHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config, room_monkey):
        # 首页Activity
        super().__init__(context, app_config, ".maintab.MaintabActivity")
        self.room_monkey = room_monkey
        self.dialog_monkey = MomoDialog(context)

    def run(self):
        return self.run_loop(0)

    def run_loop(self, deep: int):
        if deep > 3:
            log_print("循环次数超过3次，退出")
            return False
        deep += 1
        d = self.d
        self.dialog_monkey.try_dismiss()
        # 切换到底部直播tab
        live_tab = d.xpath(
            '(//*[@resource-id="com.immomo.momo:id/ll_tab_item_tv_label"])[2]')
        if self.retry_exists(live_tab, "live_tab"):
            log_print("点击底部直播tab")
            self.safe_click(live_tab, "live_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到直播tab，等待循环执行")
            time.sleep(self.action_normal_dur)
            return self.run_loop(deep)

        # 切换到推荐子tab
        recommend_tab = d.xpath('//*[@text="推荐"]')
        if self.retry_exists(recommend_tab, "recommend_tab"):
            log_print("点击推荐子tab")
            self.safe_click(recommend_tab, "recommend_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到推荐子tab，等待循环执行")
            time.sleep(self.action_normal_dur)
            return self.run_loop(deep)

        rv = d(resourceId="com.immomo.momo:id/molive_fragment_live_home_sub_fragment_rv")
        if not rv.exists:
            log_print("未找到推荐直播列表，等待循环执行")
            return self.run_loop(deep)
        tofu = rv.child(resourceId="com.immomo.momo:id/live_center_layout")
        if not tofu.exists:
            log_print("未找到直播豆腐块，等待循环执行")
            return self.run_loop(deep)
        self.safe_click(tofu, "tofu")
        time.sleep(self.action_long_dur)

        # 直播间内操作
        self.room_monkey.run()
        return True
