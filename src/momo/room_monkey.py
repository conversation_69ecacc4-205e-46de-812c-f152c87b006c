import time
from BaseMonkey import ActivityMonkey
from momo.momo_dialog import MomoDialog
from util.logger import log_print


class MomoRoomMonkey(ActivityMonkey):
    def __init__(self, context, app_config):
        # 直播间Activity
        super().__init__(context, app_config,
                         "com.immomo.molive.gui.activities.live.base.LiveActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly
        self.anchor_name = None
        self.dialog_monkey = MomoDialog(context)

    def run(self):
        fail = 0

        while self.get_process() < self.get_total_process():
            time.sleep(self.action_normal_dur)
            self.dialog_monkey.try_dismiss()

            # if i < 100:
            #     self.switch_live_room(i)
            #     self.momo_dialog.tryDismiss()
            #     continue

            # 检测现在的直播间主播名字
            anchor_name_view = self.d(
                resourceId="com.immomo.momo:id/live_tv_star_name")
            if self.retry_exists(anchor_name_view, "anchor_name_view"):
                cur_anchor_name = anchor_name_view.get_text()
                log_print(f"当前直播间主播名字: {cur_anchor_name}")
                if self.anchor_name and cur_anchor_name == self.anchor_name:
                    log_print(f"当前直播间主播名字与上次相同，跳过")
                    self.switch_live_room(self.get_process())
                    continue
                else:
                    log_print(f"当前直播间主播名字与上次不同，继续")
                    self.anchor_name = cur_anchor_name
            else:
                log_print("未找到主播名字")
                time.sleep(self.action_short_dur)
                # 尝试点退后按钮
                self.d.press("back")
                time.sleep(self.action_short_dur)
                continue

            send_result = False
            fail_reason = ""

            # 公屏聊天
            send_publicly_result, send_publicly_fail_reason = self.chat_publicly()
            # 私聊
            send_privately_result, send_privately_fail_reason = self.chat_privately()

            if send_publicly_result:
                send_result = True
            if send_publicly_fail_reason:
                fail_reason = send_publicly_fail_reason
            if send_privately_result:
                send_result = True
            if send_privately_fail_reason:
                fail_reason = send_privately_fail_reason

            if send_result:
                self._success_count += 1
            else:
                fail += 1
                if fail_reason:
                    if fail_reason in self.get_fail_reason_dict():
                        self.get_fail_reason_dict()[fail_reason] += 1
                    else:
                        self.get_fail_reason_dict()[fail_reason] = 1
            self.do_process(self.get_process() + 1)
            log_print(
                f"发送结果 {send_result}: 成功: {self.get_success_count()}, 失败: {fail}, 失败原因: {fail_reason}")
            log_print(
                f"公屏发送结果 {send_publicly_result} 失败原因: {send_publicly_fail_reason}")
            log_print(
                f"私信发送结果 {send_privately_result} 失败原因: {send_privately_fail_reason}")
            time.sleep(self.action_short_dur)

    def chat_publicly(self) -> tuple:
        if not self.text_publicly:
            return False, ""

        d = self.d
        send_result = False
        fail_reason = ""
        # 1. 点击公屏发送入口
        chat_entry = d(resourceId="com.immomo.momo:id/phone_live_tv_chat")
        if self.retry_exists(chat_entry, "chat_entry"):
            self.safe_click(chat_entry, "chat_entry")
            time.sleep(self.action_normal_dur)
            # 2. 输入内容
            input_box = d(resourceId="com.immomo.momo:id/live_edit_send_text")
            if self.retry_exists(input_box, "input_box"):
                self.safe_set_text(input_box, self.text_publicly, "input_box")
                time.sleep(self.action_short_dur)
                send_btn = d(
                    resourceId="com.immomo.momo:id/live_edit_send_btn")
                if self.retry_exists(send_btn, "send_btn"):
                    self.safe_click(send_btn, "公屏send_btn")
                    # self.d.press("enter")
                    log_print("公屏消息发送成功")
                    send_result = True
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"
        return send_result, fail_reason

    def chat_privately(self) -> tuple:
        d = self.d
        send_result = False
        fail_reason = ""
        # 1. 点击主播头像，打开卡片
        avatar = d(resourceId="com.immomo.momo:id/live_iv_star_avatar")
        if self.retry_exists(avatar, "avatar"):
            self.safe_click(avatar, "avatar")
            time.sleep(self.action_normal_dur)
            # 2. 点击卡片头像，进入个人页
            card_avatar = d(
                resourceId="com.immomo.momo:id/user_card_iv_avatar")
            if self.retry_exists(card_avatar, "card_avatar"):
                self.safe_click(card_avatar, "card_avatar")
                time.sleep(self.action_long_dur)
                # 3. 点击打招呼按钮
                greet_btn = d(
                    resourceId="com.immomo.momo:id/profile_layout_start_chat")
                if greet_btn.exists:
                    greet_btn.click()
                    time.sleep(self.action_normal_dur)
                    # 4. 聊天内容区，检查是否已发过相同文案
                    chat_rv = d(
                        resourceId="com.immomo.momo:id/message_chat_recycler_view")
                    has_sent = False
                    # 检查是否已发过相同文案，暂时不检查了，因为拿不到textview
                    # if chat_rv.exists:
                    #     idx = 0
                    #     while True:
                    #         item = chat_rv.child(index=idx)
                    #         if not item.exists:
                    #             break
                    #         textview = item.child(
                    #             resourceId="com.immomo.momo:id/message_tv_layouttextview")
                    #         if textview.exists and textview.get_text() == self.text:
                    #             log_print("已发送过相同私聊文案")
                    #             has_sent = True
                    #             fail_reason = "已经发送过文案"
                    #             break
                    #         idx += 1
                    if not has_sent:
                        # 5. 输入私聊文案
                        input_box = d(
                            resourceId="com.immomo.momo:id/message_ed_msgeditor")
                        send_btn = d(
                            resourceId="com.immomo.momo:id/message_btn_sendtext")
                        if self.retry_exists(input_box, "input_box"):
                            self.safe_set_text(
                                input_box, self.text, "input_box")
                            time.sleep(self.action_normal_dur)
                            if self.retry_exists(send_btn, "send_btn"):
                                self.safe_click(send_btn, "私聊send_btn")
                                # self.d.press("enter")
                                log_print("私聊消息发送成功")
                                send_result = True
                                time.sleep(self.action_normal_dur)
                            else:
                                log_print("打招呼-未找到发送按钮")
                                fail_reason = "打招呼-未找到发送按钮"
                            # 点击发送后会自动收回
                            # d.press("back")
                            time.sleep(self.action_normal_dur)
                        else:
                            log_print("打招呼-未找到私聊输入框")
                            fail_reason = "打招呼-未找到私聊输入框"
                    # # 6. 检查是否发送成功 (不检测了，拿不到textview)
                    # chat_rv = d(
                    #     resourceId="com.immomo.momo:id/message_chat_recycler_view")
                    # sent = False
                    # if self.retry_exists(chat_rv, "chat_rv"):
                    #     idx = 0
                    #     while True:
                    #         item = chat_rv.child(index=idx)
                    #         if not item.exists:
                    #             break
                    #         textview = item.child(
                    #             resourceId="com.immomo.momo:id/message_tv_layouttextview")
                    #         if textview.exists and textview.get_text() == self.text:
                    #             sent = True
                    #             break
                    #         idx += 1
                    # if sent:
                    #     log_print("私聊文案已成功发送")
                    # else:
                    #     log_print("未检测到私聊文案发送成功")
                    # 7. 关闭聊天窗口
                    close_btn = d(
                        resourceId="com.immomo.momo:id/bar_left_close")
                    if self.retry_exists(close_btn, "close_btn"):
                        self.safe_click(close_btn, "close_btn")
                        time.sleep(self.action_short_dur)
                    # 8. 返回个人页
                    back_btn = d(resourceId="com.immomo.momo:id/iv_back")
                    if self.retry_exists(back_btn, "back_btn"):
                        self.safe_click(back_btn, "back_btn")
                        time.sleep(self.action_short_dur)
                else:
                    log_print("未找到打招呼按钮")
                    fail_reason = "未找到打招呼按钮"
            else:
                log_print("未找到卡片头像")
                fail_reason = "未找到卡片头像"
        else:
            log_print("未找到直播间主播头像")
            fail_reason = "未找到直播间主播头像"
        return send_result, fail_reason

    def switch_live_room(self, index: int):
        width, height = self.d.window_size()
        max_attempts = 4
        for attempt in range(max_attempts):
            # 每次滑动参数有微调
            x = width * (0.6 + 0.01 * attempt)
            x_end = width * (0.6 + 0.02 * attempt)
            y_start = height * (0.4 + attempt / max_attempts)
            y_end = height * (0.4 + attempt / max_attempts -
                              0.2 * (1 + attempt / max_attempts))
            log_print(
                f"向上滑动切换下一个主播: {index + 1}/{self.app_config.count} attempt:{attempt+1} from:{x},{y_start} to:{x_end},{y_end}")
            self.d.swipe(x, y_start, x_end, y_end, 0.05)
            time.sleep(self.action_normal_dur)
            # 检查主播昵称
            anchor_name_view = self.d(
                resourceId="com.immomo.momo:id/live_tv_star_name")
            anchor_name = None
            if anchor_name_view.exists:
                try:
                    anchor_name = anchor_name_view.get_text()
                except Exception:
                    anchor_name = None
                if anchor_name is not None and anchor_name != self.anchor_name:
                    log_print(f"主播昵称已变化，切换成功, 昵称为：{anchor_name}")
                    # self.anchor_name = anchor_name
                    return
            log_print(f"主播昵称未变化，重试滑动，昵称为：{anchor_name}")
        log_print("多次滑动后主播昵称依然未变化，可能切换失败")
