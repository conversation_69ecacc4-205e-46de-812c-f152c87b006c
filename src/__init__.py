"""
BeautyCall Monkey Test Package
"""

from multiprocess_monkey_run import MultiProcessMonkeyRunInDevices
import schedule
import time
from typing import List
from monkey_config import AppMonkeyConfig
from util.keep_alive import start_keep_alive_for_scheduler, stop_keep_alive_for_scheduler
from util.logger import log_print

configs: List[AppMonkeyConfig] = []
# configs.append(AppMonkeyConfig("花椒", """视频通话美颜- 秒颜（红色书本搜）
# 可以在通话时用到【丰富妆容和滤镜、头饰】，能让您视频通话和现在一样美美嘟，【拷贝本条】免fei体验，邀请SVukd""",
#                                """秒颜app，暗号 SVukd，
# 有多样妆容、滤镜及头饰，让视频与直播一样颜值在线。""",
#                                15))
# configs.append(AppMonkeyConfig("酷狗", """🛰️ 视频通话美颜 app - 秒颜，试用邀请马Spw1z
# 可以解锁【丰富妆容和滤镜、头饰】
# 能让您视频通话实现和zhi波间一样美美嘟""",
#                                """🛰️视频美颜 app：秒颜 试用邀请马Spw1z
# 有【丰富妆容和滤镜 头饰】
# 让您视频和直波一样美""", 10))
# configs.append(AppMonkeyConfig("秀色", "🌏视频通话美颜 app - 秒颜，试用邀请马：SFdSO"))
# configs.append(AppMonkeyConfig("yy", """主播您好~我们有一款百万主播同款的微信视频美颜通话工具——秒颜
# 现在官方想选择邀请一些主播成为我们秒颜的会员 可以给您提供试用机会~
# 秒颜拥有【主播同款妆容、丰富的滤镜和头饰、手势特效】
# 可以让您在微信视频通话也自带直播间同款美颜
# 私下和大哥视频通话也会还原直播间的美貌
# 素颜也可以一键美颜上镜 妆容牢固不会掉
# 如果你愿意试用 可以用安卓手机搜索和下载"秒颜" 登录后填写邀请码：SuHu2"""))
configs.append(AppMonkeyConfig("陌陌", """视频通话美颜工具- 秒颜，邀请马SRSKg
可以用到【丰富妆容和滤镜、头饰】
能让您视频通话和现在一样美美嘟""",
                               """视频通话美颜工具- 秒颜，邀请马SRSKg
可以用到【丰富妆容和滤镜、头饰】
能让您视频通话和现在一样美美嘟""",
                               20))
# configs.append(AppMonkeyConfig("心遇", """视频通话美颜工具- 秒颜，邀请马SRSKg
# 可以用到【丰富妆容和滤镜、头饰】
# 能让您视频通话和现在一样美美嘟""",
# "您好，主播",
# 100))
# configs.append(AppMonkeyConfig("Yo语音", """地球号视频通话美颜- 秒颜
# 可以在通话时用到【丰富妆容和滤镜、头饰】，能让您视频通话和现在一样美美嘟
# 红色书本搜同名了解更多 【复制本条】免fei体验，邀请马share_code=Sf4AB""",
#                                """""",
#                                40))
# configs.append(AppMonkeyConfig("赫兹", """地球号视频通话美颜- 秒颜
# 可以在通话时用到【丰富妆容和滤镜、头饰】，能让您视频通话和现在一样美美嘟
# 红色书本搜同名了解更多 【复制本条】免fei体验""",
#                                """""",
#                                50))


def main():
    """
    主函数，运行 Monkey 测试
    """
    try:
        log_print("Starting BeautyCall Monkey Test...")
        MultiProcessMonkeyRunInDevices(configs).run()
        log_print("BeautyCall Monkey Test completed successfully!")
    except Exception as e:
        log_print(f"Error occurred: {str(e)}")


def run_main_in_scheduler():
    """
    在定时任务中运行主函数
    """
    stop_keep_alive_for_scheduler()
    main()
    start_keep_alive_for_scheduler()


def run_scheduler():
    """
    定时执行函数
    """
    log_print("定时任务已启动...")
    # 设置每天定时执行的时间，这里设置为每天上午10点执行
    schedule.every().day.at("22:35").do(run_main_in_scheduler)

    # 也可以设置多个时间点
    # schedule.every().day.at("14:00").do(run_main_in_scheduler)  # 下午2点
    # schedule.every().day.at("20:00").do(run_main_in_scheduler)  # 晚上8点

    # 或者设置每隔几小时执行一次
    # schedule.every(6).hours.do(run_main_in_scheduler)  # 每6小时执行一次

    start_keep_alive_for_scheduler()
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        log_print("定时任务被中断，正在停止智能防息屏保活...")
        stop_keep_alive_for_scheduler()
    except Exception as e:
        log_print(f"定时任务异常: {e}")
        stop_keep_alive_for_scheduler()


if __name__ == "__main__":
    # 直接执行一次
    main()

    # 启动定时任务
    # run_scheduler()
