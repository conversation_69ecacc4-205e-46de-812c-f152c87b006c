"""
BeautyCall Monkey Test Package
"""

import os
import time

import schedule

from monkey_config import MonkeyConfig
from multiprocess_monkey_run import MultiProcessMonkeyRunInDevices
from util.config_loader import load_monkey_config
from util.file_util import project_root
from util.keep_alive import (
    start_keep_alive_for_scheduler,
    stop_keep_alive_for_scheduler,
)
from util.local_data_collect import collect_app_success_total_by_date
from util.logger import log_print
from util.send_result_file import send_result_file_to_ruliu_robot


def load_configs() -> MonkeyConfig:
    config_path = os.path.join(project_root, "configs", "monkey_config_1.json")
    return load_monkey_config(config_path)


configs = load_configs()


def start_run_monkey(keep_alive: bool = False):
    """
    开始运行 Monkey
    :param keep_alive: 是否启动防息屏保活
    """
    try:
        log_print("Starting BeautyCall Monkey Test...")
        MultiProcessMonkeyRunInDevices(
            configs.app_monkey_configs, keep_alive).run()
        log_print("BeautyCall Monkey Test completed successfully!")
    except Exception as e:
        log_print(f"Error occurred: {str(e)}")


def run_main_in_scheduler():
    """
    在定时任务中运行主函数
    """
    stop_keep_alive_for_scheduler()
    start_run_monkey(keep_alive=True)
    start_keep_alive_for_scheduler()


def run_scheduler():
    """
    定时执行函数
    """
    log_print("定时任务已启动...")

    for schedule_time in configs.daily_schedule_times:
        schedule.every().day.at(schedule_time).do(run_main_in_scheduler)

    log_print(f"定时任务已启动，每天{configs.daily_schedule_times}执行")

    # 或者设置每隔几小时执行一次
    # schedule.every(6).hours.do(run_main_in_scheduler) # 每6小时执行一次

    start_keep_alive_for_scheduler()
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        log_print("定时任务被中断，正在停止智能防息屏保活...")
        stop_keep_alive_for_scheduler()
    except Exception as e:
        log_print(f"定时任务异常: {e}")
        stop_keep_alive_for_scheduler()


def start_with_terminal():
    print("请选择运行模式：")
    print("1. 直接运行自动化脚本")
    print("2. 定时任务运行")
    print("3. 收集数据统计")
    print("4. 发送result文件数据到如流")
    mode = input("请输入选项(1/2/3/4): ").strip()
    if mode == "1":
        start_run_monkey(keep_alive=False)
    elif mode == "2":
        run_scheduler()
    elif mode == "3":
        try:
            start_date = input("请输入日期（年-月-日）: ").strip()
            # end_date = input("请输入结束日期（年-月-日）: ").strip()
            collect_app_success_total_by_date(start_date, None)
        except Exception as e:
            print(f"输入有误: {e}")
    elif mode == "4":
        result_relative_file_name = input(
            "请输入result的json文件路径（results目录下的相对路径）："
        )
        send_result_file_to_ruliu_robot(
            result_relative_file_name=result_relative_file_name
        )
    else:
        print("无效选项，退出。")


if __name__ == "__main__":
    # main()
    start_with_terminal()
