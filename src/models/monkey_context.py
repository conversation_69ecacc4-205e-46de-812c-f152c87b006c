import uiautomator2 as u2
from uiautomator2 import Device


class MonkeyContext:
    def __init__(self):
        self.__result_dir_path: str or None = None
        self.__ui_automator_device: Device or None = None

    def get_result_dir_path(self):
        return self.__result_dir_path

    def set_result_dir_path(self, result_path: str):
        self.__result_dir_path = result_path

    def get_ui_automator_device(self):
        return self.__ui_automator_device

    def set_ui_automator_device(self, ui_automator_device: u2.Device):
        self.__ui_automator_device = ui_automator_device
