class TerminatedMonkeyError(Exception):
    CODE_PROCESS_FINISHED = 1
    MSG_PROCESS_FINISHED = "任务已完成"

    CODE_TERMINATED_APP_RUN = 200
    MSG_TERMINATED_APP_RUN = "终止应用运行任务"

    CODE_TERMINATED_APP_RUN_VERSION_ERROR = 201
    MSG_TERMINATED_APP_RUN_VERSION_ERROR = "终止应用运行任务-版本异常"

    CODE_TERMINATED_APP_RUN_ACCOUNT_ERROR = 202
    MSG_TERMINATED_APP_RUN_ACCOUNT_ERROR = "终止应用运行任务-账号异常"

    @staticmethod
    def create_process_finished():
        return TerminatedMonkeyError(TerminatedMonkeyError.CODE_PROCESS_FINISHED,
                                     TerminatedMonkeyError.MSG_PROCESS_FINISHED)

    @staticmethod
    def create_terminated_app_run(reason: str = None):
        return TerminatedMonkeyError(TerminatedMonkeyError.CODE_TERMINATED_APP_RUN,
                                     TerminatedMonkeyError.MSG_TERMINATED_APP_RUN, reason)

    @staticmethod
    def create_terminated_app_run_version_err(reason: str = None):
        return TerminatedMonkeyError(TerminatedMonkeyError.CODE_TERMINATED_APP_RUN_VERSION_ERROR,
                                     TerminatedMonkeyError.MSG_TERMINATED_APP_RUN_VERSION_ERROR, reason)

    @staticmethod
    def create_terminated_app_run_account_err(reason: str = None):
        return TerminatedMonkeyError(TerminatedMonkeyError.CODE_TERMINATED_APP_RUN_ACCOUNT_ERROR,
                                     TerminatedMonkeyError.MSG_TERMINATED_APP_RUN_ACCOUNT_ERROR, reason)

    """终止任务异常"""

    def __init__(self, code: int, message: str = "终止任务", extra_msg: str = None):
        self.code = code
        self.message = message
        self.extra_msg = extra_msg
        super().__init__(self.message)

    def __str__(self):
        return f"TerminatedMonkeyError: {self.code} {self.message} {self.extra_msg}"
