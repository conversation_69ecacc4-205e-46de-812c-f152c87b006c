from BaseMonkey import A<PERSON><PERSON>onkey
from monkey_config import AppMonkeyConfig
from ttyy.ttyy_home import Ttyy<PERSON><PERSON>Monkey
from ttyy.ttyy_room import Ttyy<PERSON>oomMonkey
from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext

# TTYY App 自动灌水脚本


class TtyyAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = TtyyRoomMonkey(context, app_config)
        self.home_monkey = TtyyHomeMonkey(
            context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey

    def get_app_package_name(self) -> str:
        return "com.ttyy.app"  # 需要根据实际的TTYY包名修改

    def run(self):
        # 执行首页逻辑
        self.home_monkey.run()
        log_print("TTYY自动私聊任务完成！")
