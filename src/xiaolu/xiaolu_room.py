# -*- coding: utf-8 -*-
import time
from typing import List
from BaseMonkey import ActivityMonkey
from xiaolu.xiaolu_dialog import XiaoluDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import Device
from xiaolu.xiaolu_im import XiaoluIMMonkey
from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext



# 小鹿直播间脚本
class XiaoluRoomMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        # 直播间Activity - 需要根据实际的小鹿应用Activity修改
        super().__init__(context, app_config, ".voiceroom.VoiceRoomActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly or app_config.text
        # self.action_short_dur = 3
        # self.action_normal_dur = 6
        # self.action_long_dur = 10
        self.dialog_monkey = XiaoluDialog(context)
        self.send_publicly_result = False
        self.send_publicly_fail_reason = ""
        self.send_privately_result_list: List[bool] = [False]
        self.send_privately_fail_reason_list: List[str] = [""]
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"room check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)

    def run(self):
        log_print("**** 进入聊天室 ****")
      
        d = self.d
        time.sleep(self.action_normal_dur)
        
        self.do_process(1)
        
        # 1. 私聊所有用户头像
        self.chat_all_users_privately(d)
        
         # 2. 公屏发送消息    #fizzc 先屏蔽发送公屏
        # send_publicly_result, send_publicly_fail_reason = self.chat_publicly(d)
        # self.send_publicly_result = send_publicly_result
        # self.send_publicly_fail_reason = send_publicly_fail_reason
        
        cur_activity = self.d.app_current()["activity"]
        log_print(f"直播间操作完成 cur_activity:{cur_activity}")
        time.sleep(self.action_normal_dur)
        if self.d.app_current()["activity"] == self.activity or self.d.app_current()["activity"] != ".activity.MainActivity":
            log_print("当前 activity 不是 home，返回")
            self.d.press("back")

        return True

    def chat_publicly(self, d):
        """公屏发送消息"""
        send_result = False
        fail_reason = ""
        # 点击公屏发送入口
        chat_entry = d(resourceId="com.zjwx.xiaolu:id/input")
        if self.retry_exists(chat_entry, "chat_entry"):
            self.safe_click(chat_entry, "chat_entry")
            time.sleep(self.action_short_dur)
            
            # 输入内容
            input_box = d(resourceId="com.zjwx.xiaolu:id/dialog_input_content")
            if self.retry_exists(input_box, "input_box"):
                input_box.set_text(self.text_publicly)
                time.sleep(self.action_short_dur)
                
                # 点击发送
                send_btn = d(resourceId="com.zjwx.xiaolu:id/dialog_send")
                if self.retry_exists(send_btn, "send_btn"):
                    self.safe_click(send_btn, "send_btn")
                    send_result = True
                    log_print("公屏消息发送成功")
                    d.press("back")
                    time.sleep(self.action_short_dur)
                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"

        return send_result, fail_reason


    def chat_all_users_privately(self, d):
        """私聊所有用户头像"""
        # 在线用户区域
        online_users = d(resourceId="com.zjwx.xiaolu:id/recyclemei")
        
        if not self.retry_exists(online_users, "online_users", 3):
            log_print("未找到有效用户")
            return
        
        avatar_users = online_users.child(resourceId="com.zjwx.xiaolu:id/meikongview")
        if not self.retry_exists(avatar_users, "avatar_user"):
            log_print("未找到有效用户")
            return
        # 获取所有头像的数量
        avatar_count = avatar_users.count
        log_print(f"找到 {avatar_count} 个用户头像")
        self._total_process = avatar_count # +1是公屏
        self.send_privately_result_list = [False] * avatar_count
        self.send_privately_fail_reason_list = [""] * avatar_count
        # if avatar_count > 3:
        #     avatar_count = 2
        # 遍历所有头像
        for i in range(avatar_count):
            try:
                # 获取第i个头像
                avatar = avatar_users[i]
                if self.retry_exists(avatar, "avatar", 3):
                    nickname = avatar.child(resourceId="com.zjwx.xiaolu:id/nikemane")
                    if nickname.exists:
                        nickText = nickname.get_text()
                        log_print(f"找到第{i + 1}个用户头像的昵称：{nickText}")
                        if "号麦位" in nickText:
                            log_print(f"第{i + 1}个坑位没上麦，跳过")
                            continue
                    log_print(f"点击第 {i+1} 个用户头像")
                    self.safe_click(avatar, "avatar")
                    log_print(f"点击第 {i+1} 个用户头像成功")
                    time.sleep(self.action_normal_dur)
                    if self.is_self():
                        continue
                    # 弹出卡片后
                    # 获取昵称
                    user_nick_view = self.d(resourceId="com.zjwx.xiaolu:id/tx_nick_name")
                    if self.retry_exists(user_nick_view, "user_nick_view", 3):
                        nick_name = user_nick_view.get_text()
                        log_print(f"找到第{i + 1}个用户头像的昵称：{nick_name}")
                    else:
                        nick_name = "Unknown"
                        log_print(f"找不到第{i + 1}个用户头像的昵称！")

                    if self.follow(nick_name):
                        self.safe_click(avatar, "avatar")
                        time.sleep(self.action_short_dur)
                    
                    # 执行私聊步骤
                    send_privately_result, send_privately_fail_reason = self.chat_single_user_privately(d)
                    self.send_privately_result_list[i] = send_privately_result
                    self.send_privately_fail_reason_list[i] = send_privately_fail_reason
                    
                    time.sleep(self.action_short_dur)
                    if send_privately_fail_reason == "ignoreUser":
                        continue
                    self.do_process(i + 1 + 1) # + 1是当前头像进度
                    time.sleep(15)
                    log_print(f"15 delay for next！")
                else:
                    log_print(f"第 {i+1} 个头像不存在")
                    break
            except Exception as e:
                if isinstance(e, TerminatedMonkeyError):
                    raise
                else:
                    log_print(f"处理第 {i+1} 个头像时出错: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    continue

    def is_self(self) -> bool:
        im_entry = self.d(resourceId="com.zjwx.xiaolu:id/to_chat")
        if not im_entry.exists:
            log_print(f"为本人弹窗！！")
            return True
        return False

    def follow(self, nick_name: str) -> bool:
        follow_view = self.d(resourceId="com.zjwx.xiaolu:id/to_faucs")
        if self.retry_exists(follow_view, "查找关注按钮", 3):
            follow_status = follow_view.get_text()
            if follow_status == "关注":
                log_print(f"找到关注按钮，点击关注 {nick_name}")
                self.safe_click(follow_view)
                time.sleep(self.action_short_dur)
                return True
            elif follow_status == "已关注":
                log_print(f"已经关注了 {nick_name}")
            else:
                log_print(f"无法确定关注状态 {nick_name}")
        else:
            log_print(f"找不到关注按钮，无法关注 {nick_name}")
            
        return False

    def chat_single_user_privately(self, d: Device):
        """对单个用户执行私聊步骤"""
        send_result = False
        fail_reason = ""
        # 点击发消息按钮
        # com.xiaolu.app:id/tv_go_chat
        message_btn = d(resourceId="com.zjwx.xiaolu:id/to_chat")
        if self.retry_exists(message_btn, "message_btn", 3):
            log_print(f"单聊 点击发消息按钮")
            self.safe_click(message_btn, "message_btn")
            log_print(f"单聊 点击发消息按钮成功")
            time.sleep(self.action_normal_dur)
       
            im_monkey = XiaoluIMMonkey(self.context, self.app_config)
            return im_monkey.run()
        return send_result, fail_reason
