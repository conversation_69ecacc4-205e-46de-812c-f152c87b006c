# -*- coding: utf-8 -*-
import time

from BaseMonkey import BaseDialog
from util.logger import log_print


class XiaoluDialog(BaseDialog):
    def __init__(self, context):
        super().__init__(context)

    def close_normal_tip_dialog(self):
        """语音房弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="提示"]')
        if tmp_dialog.exists:
            log_print("提示弹窗，点击取消")
            confirm_btn = self.d.xpath('//*[@text="取消"]')
            confirm_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_sign_tip_dialog(self):
        """签到弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="断签将从第1天开始领取"]')
        if tmp_dialog.exists:
            log_print("签到弹窗，点击取消")
            close_btn = self.d(resourceId="com.zjwx.xiaolu:id/image_close")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_splashAd_dialog(self):
        """广告弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="点击跳转至详情页或第三方应用 "]')
        if tmp_dialog.exists:
            log_print(f"广告闪屏弹窗，点击取消")
            close_btn = self.d.xpath('//*[@text="跳过"]')
            close_btn2 = self.d.xpath('//*[@text="跳过 "]')
            if close_btn.exists:
                close_btn.click()
            if close_btn2.exists:
                close_btn2.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_im_warn_dialog(self):
        """im聊天弹窗"""
        dialog_content = self.d(resourceId="com.zjwx.xiaolu:id/dialog_double_select_content")
        dialog_title = self.d(resourceId="com.zjwx.xiaolu:id/dialog_double_select_tilte")
        if dialog_content.exists and dialog_title.exists:
            log_print("IM聊天弹窗，点击取消")
            close_btn = self.d.xpath('//*[@text="知道了"]')
            if close_btn.exists:
                close_btn.click()
            else:
                log_print(f"IM聊天弹窗，点击返回")
                self.d.press("back")
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_login_warn_dialog(self):
        """登录弹窗"""
        dialog_content = self.d(resourceId="com.zjwx.xiaolu:id/im_desc")
        dialog_close = self.d(resourceId="com.zjwx.xiaolu:id/close")
        if dialog_content.exists and dialog_close.exists:
            log_print("登录弹窗，点击取消")
            dialog_close.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_forbid_warn_dialog(self):
        """封禁弹窗"""
        dialog_content = self.d.xpath('//*[@text="违规申诉"]')
        dialog_close = self.d.xpath('//*[@text="关闭"]')
        if dialog_content.exists and dialog_close.exists:
            log_print("封禁弹窗，点击取消")
            dialog_close.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_bindPhone_warn_dialog(self):
        """绑定手机号弹窗"""
        dialog_content = self.d.xpath('//*[@text="手机号绑定"]')
        dialog_close = self.d(resourceId="com.zjwx.xiaolu:id/finsh")
        if dialog_content.exists and dialog_close.exists:
            log_print("bindPhone弹窗，点击取消")
            dialog_close.click()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False


    def try_dismiss(self):
        result = False
        if self.close_splashAd_dialog():
            result = True
        if self.close_normal_tip_dialog():
            result = True
        if self.close_sign_tip_dialog():
            result = True
        if self.close_im_warn_dialog():
            result = True
        if self.close_login_warn_dialog():
            result = True
        if self.close_forbid_warn_dialog():
            result = True
        if self.close_bindPhone_warn_dialog():
            result = True
        return result
