# -*- coding: utf-8 -*-
import time
from BaseMonkey import App<PERSON>onkey
from monkey_config import AppMonkeyConfig
from xiaolu.xiaolu_home import <PERSON><PERSON><PERSON><PERSON>Monkey
from xiaolu.xiaolu_room import <PERSON><PERSON><PERSON><PERSON><PERSON>onkey

from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext


# 小鹿 App 自动灌水脚本
class XiaoluAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = XiaoluRoomMonkey(context, app_config)
        self.home_monkey = XiaoluHomeMonkey(context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self) -> str:
        return "com.zjwx.xiaolu"  # 需要根据实际的小鹿包名修改

    def run(self):
        # 执行首页逻辑
        self.home_monkey.run()
        
        log_print("小鹿自动任务完成！")
