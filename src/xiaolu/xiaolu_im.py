# -*- coding: utf-8 -*-
import time

from BaseMonkey import ActivityMonkey
from xiaolu.xiaolu_dialog import XiaoluDialog
from util.logger import log_print
from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext


class XiaoluIMMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        # Im Activity - 需要根据实际的小鹿应用Activity修改
        super().__init__(context, app_config, ".message.messageui.ChatActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly or app_config.text
        self.dialog_monkey = XiaoluDialog(context)
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"im check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)

    def run(self):
        log_print("start to process im......")
        self.dialog_monkey.try_dismiss()
        return self.start_send_text(self.d)

    
    def start_send_text(self, d):
        time.sleep(self.action_short_dur)
        send_result = False
        fail_reason = "当前 activity 不是 IM "
        if d.app_current()["activity"] == ".message.messageui.ChatActivity":
            log_print("准备发送私聊")
            return self.send_text_to(d)
        log_print(fail_reason)
        closeBtn = d(resourceId="com.zjwx.xiaolu:id/title_back")
        if closeBtn.exists:
            closeBtn.click()
        else:
            self.close_im_wind(d)
        return send_result, fail_reason
    
    def send_text_to(self, d):

        send_result = False
        fail_reason = ""
        chat_container = d(resourceId="com.zjwx.xiaolu:id/list")
        if self.retry_exists(chat_container, "chat_container", 2):
            rightMsg = chat_container.child(resourceId="com.zjwx.xiaolu:id/rightMessage")
            if self.retry_exists(rightMsg, "rightMsg", 2):
                log_print("已发送过相同私聊文案")
                self.close_im_wind(d)
                return send_result, "ignoreUser"

         # 输入私聊文案
        input_box = d(resourceId="com.zjwx.xiaolu:id/input")
        nicknametext = d(resourceId="com.zjwx.xiaolu:id/title")
        if self.retry_when_false(lambda: input_box.exists):
            input_box.click()
            time.sleep(self.action_short_dur)
            self.dialog_monkey.try_dismiss()
            prefix = ""
            if nicknametext.exists:
                nickname = nicknametext.get_text()
                if nickname:
                    prefix = "哈喽" + nickname + " "
            input_box.set_text(f"{prefix} " + self.text)
            log_print(input_box.get_text())
            send_btn = d(resourceId ="com.zjwx.xiaolu:id/sendtext")
            if self.retry_when_false(lambda: send_btn.exists):
                send_btn.click()
                log_print("私聊消息发送成功")
                time.sleep(self.action_short_dur)
                # 检查是否发送成功
                chat_container = d(resourceId="com.zjwx.xiaolu:id/list")
                message_list = chat_container.child(resourceId="com.zjwx.xiaolu:id/romevemsg")
                message_count = message_list.count
                log_print(f"找到 聊天记录 {message_count}")
                sent = False
                if message_count > 0:
                    lastMsg = message_list[message_count - 1]
                    if lastMsg.exists:
                        sendresult = lastMsg.child(resourceId="com.zjwx.xiaolu:id/error")
                        if sendresult.exists and sendresult.get_text() == '送达':
                            sent = True
                if sent:
                    log_print("私聊文案已成功发送")
                    send_result = True
                else:
                    log_print("未检测到私聊文案发送成功")
                    fail_reason = "未检测到私聊文案发送成功"
            else:
                log_print("未找到私聊发送按钮")
                fail_reason = "未找到私聊发送按钮"                    
        else:
            log_print("未找到私聊输入框")
            fail_reason = "未找到私聊输入框"
            
        self.close_im_wind(d)
    
        return send_result, fail_reason
        
    def close_im_wind(self, d):
        log_print("关闭私聊")
        backBtn = d(resourceId="com.zjwx.xiaolu:id/title_back")
        if backBtn.exists:
            backBtn.click()
        else:
            # 第一次back是为了关闭软键盘.
            d.press("back")
            time.sleep(1.5)
            # 判断一下是否还在IM页面,如果在的话在back一次.
            if d.app_current()["activity"] == ".message.messageui.ChatActivity":
                d.press("back")
