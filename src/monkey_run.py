import json
import os
import time
from typing import List
from BaseMonkey import AppMonkey, BaseAuto
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_auto_factory import MonkeyAutoFactory
from monkey_config import AppMonkeyConfig
from robot.ruliu_robot import create_meiyan_robot
import uiautomator2 as u2
from util.logger import log_print, set_log_context
from util.file_util import project_root
from models.monkey_context import MonkeyContext


class MonkeyRun:
    def __init__(self, app_configs: List[AppMonkeyConfig], device_serial: str = None):
        self.context = MonkeyContext()
        self.context.set_ui_automator_device(u2.connect(device_serial))
        self.context.set_result_dir_path(
            f"{project_root}/results/{device_serial}_{int(time.time())}")
        self.device_serial = device_serial
        self.app_configs = app_configs
        self.monkeys: List[BaseAuto] = [MonkeyAutoFactory.create(
            app_config, self.context) for app_config in app_configs]
        self.crash_msgs = {}
        self.run_start_time = 0
        self.run_end_time = 0

        # 设置日志上下文
        set_log_context(device_serial=device_serial)

    def run(self):
        if not self.monkeys:
            log_print("没有运行任何monkey，跳过运行")
            return
        # self.test()
        self.run_start_time = int(time.time() * 1000)
        self.run_monkeys()
        self.run_end_time = int(time.time() * 1000)
        self.process_result()

    def run_monkeys(self):
        for monkey in self.monkeys:
            self.run_monkey(monkey)

    def snapshot(self):
        self.context.get_ui_automator_device().snapshot(
            f"{self.context.get_result_dir_path()}/snapshot_{time.time()}.png")

    def run_monkey(self, monkey: BaseAuto):
        # 为当前应用设置日志上下文
        set_log_context(device_serial=self.device_serial,
                        app_name=monkey.app_config.name)
        retry_count = 0
        def process_callback(process, total_process): return log_print(
            f"MonkeyRun {monkey.app_config.name} 进度: {process}/{total_process}")
        while retry_count < 5:
            try:
                monkey.unregister_progress_callback(process_callback)
                monkey.register_progress_callback(process_callback)
                if isinstance(monkey, AppMonkey):
                    monkey.run_app()
                else:
                    monkey.run()
                break
            except Exception as e:
                log_print(f"{monkey.app_config.name} 发生异常：{e}")
                if isinstance(e, TerminatedMonkeyError) and e.code == TerminatedMonkeyError.CODE_TERMINATED_APP_RUN:
                    # crash_msgs的key是app_name，value是异常列表
                    if monkey.app_config.name in self.crash_msgs:
                        self.crash_msgs[monkey.app_config.name].append(
                            e.extra_msg or e.message)
                    else:
                        self.crash_msgs[monkey.app_config.name] = [
                            e.extra_msg or e.message]
                    if "账号异常" in e.extra_msg:
                        break
                else:
                    if monkey.app_config.name in self.crash_msgs:
                        self.crash_msgs[monkey.app_config.name].append(str(e))
                    else:
                        self.crash_msgs[monkey.app_config.name] = [str(e)]
                    import traceback
                    traceback.print_exc()
                monkey.screenshot()
                retry_count += 1
                log_print(f"发生异常后进行第{retry_count}次重试")
                continue

        if monkey.get_fail_reason_dict():
            log_print(f"\n{monkey.app_config.name} 失败原因统计：")
            for reason, count in monkey.get_fail_reason_dict().items():
                log_print(f"{reason}: {count} 次")
        else:
            log_print(f"{monkey.app_config.name} 无失败原因！")
        log_print(
            f"{monkey.app_config.name} 成功次数: {monkey.get_success_count()}, 总共运行次数: {monkey.get_total_process()}")

    def process_result(self):
        app_result = {}
        for monkey in self.monkeys:
            app_result[monkey.app_config.name] = {
                "success_count": monkey.get_success_count(),
                "total_count": monkey.get_total_process(),
                "fail_reason": monkey.get_fail_reason_dict(),
                "terminated_reason": self.crash_msgs.get(monkey.app_config.name, [])
            }
        try:
            dir = self.monkeys[0].context.get_result_dir_path()
            os.makedirs(f"{dir}", exist_ok=True)  # 确保目录存在
            with open(f"{dir}/result_{time.time()}.json", "w", encoding="utf-8") as f:
                json.dump(app_result, f, ensure_ascii=False)
        except Exception as e:
            log_print(f"写入结果文件失败: {e}")
        result = {
            "app_result": app_result,
            "run_start_time": self.run_start_time,
            "run_end_time": self.run_end_time
        }
        self.send_meiyan_group_robot(result)

    def send_meiyan_group_robot(self, result):
        report_str = generate_report_text(result)
        log_print("报告文案：")
        log_print(report_str)
        # 放在子进程用不了input
        # while True:
        #     input_str = input("是否发送如流通知消息？(y/n): ")
        #     if input_str == "y":
        #         log_print("同意，发送消息")
        #         Create_Meiyan_Robot().send_message_text(report_str)
        #         break
        #     elif input_str == "n":
        #         log_print("不发送消息")
        #         break
        #     else:
        #         log_print("请输入 'y' 或 'n'！")
        create_meiyan_robot().send_message_text(report_str)

    def test(self):
        try:
            # if len(self.monkeys) > 0:
            #     first_monkey = self.monkeys[0]
            #     proxy_monkey = first_monkey.proxy_monkey
            #     if (proxy_monkey):
            #         proxy_monkey._success_count = 3
            #         proxy_monkey._total_process = 5
            #         proxy_monkey._fail_reason_dict = {
            #             "未知": 1,
            #             "找不到头像控件": 1,
            #         }
            #     log_print(f"first_monkey: {first_monkey.get_success_count()}/{first_monkey.get_total_process()}")
            #     log_print(f"first_monkey: {first_monkey._success_count}/{first_monkey._total_process}")
            # self.process_result()
            # 读取result_1751461814.790705.json文件，转成json对象，
            with open(f"{project_root}/results/result_test.json", "r", encoding="utf-8") as f:
                result = json.load(f)
            self.send_meiyan_group_robot(result)
        except Exception as e:
            log_print(f"test error: {e}")
            import traceback
            traceback.print_exc()


def generate_report_text(result):
    app_result = result.get('app_result', {})
    run_start_time = result.get('run_start_time', 0)
    run_end_time = result.get('run_end_time', 0)
    report_run_situation_str = ""
    report_fail_str = ""
    for app_name, data in app_result.items():
        report_run_situation_str += f"{app_name} 成功次数: {data['success_count']}, 总共运行次数: {data['total_count']}\n"
        fail_reason = data.get('fail_reason', {})
        if fail_reason:
            report_fail_str += f"{app_name}：\n"
            for reason, count in fail_reason.items():
                report_fail_str += f"    {reason}: {count} 次\n"
    report_run_situation_str = report_run_situation_str.rstrip("\n")
    report_fail_str = report_fail_str.rstrip("\n")
    report_str = f"运行情况：\n{report_run_situation_str}"
    if report_fail_str:
        report_str += f"\n失败原因：\n{report_fail_str}"

    report_terminated_str = ""
    for app_name, data in app_result.items():
        terminated_reason = data.get('terminated_reason', [])
        if terminated_reason:
            report_terminated_str += f"\n    {app_name}: {'\n'.join(terminated_reason)}"

    if report_terminated_str:
        report_str += f"\n终止原因：{report_terminated_str}"
    if run_start_time > 0 and run_end_time > 0:
        # 总耗时
        total_time = (run_end_time - run_start_time) / 1000 / 60
        report_str += f"\n总耗时：{total_time:.2f} 分钟"
    return report_str