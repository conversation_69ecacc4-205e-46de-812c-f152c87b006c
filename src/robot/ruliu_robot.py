import time
import requests
import json

from util.logger import log_print

class BodyMaker:
    def __init__(self):
        self.__body = []

    def text(self, content: str):
        self.__body.append({
            "type": "TEXT",
            "content": content
        })
        return self

    def at(self, user_id_list: list[str], at_all: bool = False):
        self.__body.append({
            "type": "AT",
            "atuserids": user_id_list,
            "atall": at_all
        })
        return self
    
    def markdown(self, content: str):
        self.__body.append({
            "type": "MD",
            "content": content
        })
        return self

    def image(self, image_url: str):
        self.__body.append({
            "type": "IMAGE",
            "content": image_url
        })
        return self
    
    def get_body(self):
        return self.__body


class RuliuRobot:
    # 不知道这个群id是什么，它不是群号？加上后不知道发到哪里去了，显示发送成功，但是没有收到消息
    def __init__(self, webhook: str, toid: list[int]):
        self.webhook = webhook
        self.toid = toid
        self.headers = {
            "Content-Type": "application/json",
            # "toid": json.dumps(self.toid),
            "totype": "GROUP",
            "msgtype": "TEXT",
            "role": "robot"
        }

    def send_message(self, body: BodyMaker):
        self.headers["msgtype"] = body.get_body()[0]["type"]
        self.headers["clientmsgid"] = json.dumps(int(time.time()))
        data = {
            "message": {
                "header": self.headers,
                "body": body.get_body()
            }
        }
        try:
            log_print("RuliuRobot send_message start..")
            response = requests.post(self.webhook, headers=self.headers, data=json.dumps(data))
            log_print(f"RuliuRobot send_message code: {response.status_code}, response: {response.text}")
            return response.status_code, response.text
        except Exception as e:
            log_print(f"RuliuRobot send_message error: {e}")
            return 1001, str(e)
    
    def send_message_text(self, text: str):
        body = BodyMaker()
        body.text(text)
        return self.send_message(body)
    
    def send_message_markdown(self, markdown: str):
        body = BodyMaker()
        body.markdown(markdown)
        return self.send_message(body)
    
    def send_message_image(self, image_url: str):
        body = BodyMaker()
        body.image(image_url)
        return self.send_message(body)
    

def create_meiyan_robot():
    return RuliuRobot(
        webhook="http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d79510b66f4dd0639379497a149b5691a",
        toid=[11472138]
    )