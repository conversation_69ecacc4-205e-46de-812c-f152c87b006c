
from typing import List
from monkey_config import AppMonkeyConfig
from monkey_run import Monkey<PERSON><PERSON>
import adbutils
from util.logger import log_print, set_log_context
import multiprocessing


def monkey_run_invoke(configs: List[AppMonkeyConfig], device_serial: str):
    # 设置当前进程的日志上下文
    set_log_context(device_serial=device_serial)
    log_print(f"开始运行设备 {device_serial} 的自动化脚本")
    MonkeyRun(configs, device_serial).run()
    log_print(f"设备 {device_serial} 的自动化脚本运行完成")

# 多设备多进程运行，每个设备一个进程


class MultiProcessMonkeyRunInDevices:
    def __init__(self, configs: List[AppMonkeyConfig], host: str = "127.0.0.1", port: int = 5037):
        self.configs = configs
        self.host = host
        self.port = port

    def run(self):
        adb = adbutils.AdbClient(host=self.host, port=self.port)
        log_print(f"adb 连接成功，设备列表: {adb.device_list()}")
        processes = []
        for info in adb.list(extended=True):
            log_print(
                f"设备: {info.serial}, 状态: {info.state}, 传输ID: {info.transport_id}")
            process = multiprocessing.Process(
                target=monkey_run_invoke, args=(self.configs, info.serial))
            process.start()
            processes.append(process)
        for process in processes:
            process.join()
