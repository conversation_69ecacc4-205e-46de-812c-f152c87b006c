
from typing import Dict, List
from monkey_config import AppMonkeyConfig
from monkey_run import Monkey<PERSON><PERSON>
import adbutils
from util.logger import log_print, set_log_context
import multiprocessing
import time
from util.keep_alive import keep_alive_manager


def monkey_run_invoke(configs: List[AppMonkeyConfig], device_serial: str):
    # 设置当前进程的日志上下文
    set_log_context(device_serial=device_serial)
    log_print(f"开始运行设备 {device_serial} 的自动化脚本")
    MonkeyRun(configs, device_serial).run()
    log_print(f"设备 {device_serial} 的自动化脚本运行完成")

# 多设备多进程运行，每个设备一个进程


class MultiProcessMonkeyRunInDevices:
    def __init__(self, configs: List[AppMonkeyConfig], keep_alive: bool = False, host: str = "127.0.0.1", port: int = 5037):
        self.configs = configs
        self.keep_alive = keep_alive
        self.host = host
        self.port = port

    def run(self):
        adb = adbutils.AdbClient(host=self.host, port=self.port)
        log_print(f"adb 连接成功，设备列表: {adb.device_list()}")
        processes: List[multiprocessing.Process] = []
        device_map: Dict[int, str] = {}
        for info in adb.list(extended=True):
            log_print(
                f"设备: {info.serial}, 状态: {info.state}, 传输ID: {info.transport_id}")
            process = multiprocessing.Process(
                target=monkey_run_invoke, args=(self.configs, info.serial))
            process.start()
            processes.append(process)
            device_map[process.pid] = info.serial

        while processes:
            for p in processes[:]:
                if not p.is_alive():
                    p.join()
                    if self.keep_alive:
                        device_serial = device_map.get(p.pid)
                        if device_serial:
                            # 跑完后重新启动防息屏保活
                            keep_alive_manager.start_keep_alive(device_serial)
                    processes.remove(p)
            time.sleep(2)
