
from BaseMonkey import <PERSON><PERSON><PERSON>
from huajiao.hua<PERSON>ao_auto import Hu<PERSON><PERSON>o<PERSON><PERSON>
from kugou.kugou_auto import AutoKugou
from momo.momo_auto import Momo<PERSON><PERSON>
from hezi.hezi_auto import Hezi<PERSON><PERSON>
from monkey_config import AppMonkeyConfig
from xinyu.xinyu_auto import XinyuAuto
from xiuse.xiuse_auto import Auto<PERSON>iuse
from yomi.yomi_auto import YomiAuto
from yy.yy_auto import AutoYY
from ttyy.ttyy_auto import TtyyAuto
from models.monkey_context import MonkeyContext


class MonkeyAutoFactory:

    @staticmethod
    def create(app_config: AppMonkeyConfig, context: MonkeyContext) -> BaseAuto:
        app_name = app_config.name
        if app_name == "huajiao" or app_name == "花椒":
            return HuajiaoAuto(context, app_config)
        if app_name == "xiuse" or app_name == "秀色":
            return AutoXiuse(context, app_config)
        if app_name == "yy" or app_name == "yy直播":
            return AutoYY(context, app_config)
        if app_name == "kugou" or app_name == "酷狗":
            return AutoKugou(context, app_config)
        if app_name == "momo" or app_name == "陌陌":
            return MomoAuto(context, app_config)
        if app_name == "xinyu" or app_name == "心遇":
            return XinyuAuto(context, app_config)
        if app_name == "hezi" or app_name == "赫兹":
            return HeziAuto(context, app_config)
        if app_name == "yomi" or app_name == "Yo语音":
            return YomiAuto(context, app_config)
        if app_name == "ttyy" or app_name == "TT语音":
            return TtyyAuto(context, app_config)
        else:
            raise ValueError(f"不支持的app: {app_name}")
