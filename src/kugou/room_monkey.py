import time
from BaseMonkey import ActivityMonkey
from kugou.kugou_dialog import KugouDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from util.logger import log_print
from models.monkey_context import MonkeyContext


class RoomMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config,
                         ".allinone.watch.liveroominone.media.FALiveRoomInOneActivity")
        self.text = app_config.text
        self.dialog_monkey = KugouDialog(context)
        self.anchor_name_list = [str]

    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"room check_when_retry {tag} {i}")
        # 如果有安全验证，就直接终止整个酷狗运行
        phoneVertify = self.d.xpath('//*[@text="酷狗安全验证"]')
        if phoneVertify.exists:
            log_print("找到手机验证弹窗，终止酷狗任务")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            raise TerminatedMonkeyError.create_terminated_app_run("手机号验证")
        else:
            self.dialog_monkey.try_dismiss()
            time.sleep(self.action_normal_dur)

    def run(self):
        try:
            self.run_room()
        except TerminatedMonkeyError as e:
            if e.code == 101:
                log_print(f"直播间任务终止: {e.message}")
                return False
            else:
                raise e

    def run_room(self):
        d = self.d
        time.sleep(self.action_normal_dur)
        fail = 0

        # 酷狗每天私聊不认识的人只有10次
        while self._process < self._total_process or self._success_count > 10:
            time.sleep(self.action_normal_dur)
            self.dialog_monkey.try_dismiss()
            send_result = False
            fail_reason = ""

            # if i < 100:
            #     self.switch_live_room(i)
            #     continue

            # 检测现在的直播间主播名字
            anchor_name_view = d(resourceId="com.kugou.fanxing:id/glz")
            if self.retry_exists(anchor_name_view, "主播名字"):
                cur_anchor_name = anchor_name_view.get_text()
                log_print(f"当前直播间主播名字: {cur_anchor_name}")
                # 如果主播名字在列表中，则跳过
                if cur_anchor_name in self.anchor_name_list:
                    log_print(f"当前直播间主播名字在列表中，跳过")
                    self.hit_fail_action("当前直播间主播名字在列表中，跳过")
                    self.switch_live_room(self._process)
                    continue
                else:
                    log_print(f"当前直播间主播名字不在列表中，继续")
                    self.anchor_name_list.append(cur_anchor_name)

            else:
                activity = self.d.app_current()["activity"]
                log_print(f"未找到主播名字，当前activity: {activity}")
                if activity == ".modul.mainframe.ui.MainFrameActivity":
                    log_print("当前activity是主页，终止直播间任务")
                    break
                else:
                    # 尝试点退后按钮
                    d.press("back")
                    time.sleep(self.action_short_dur)
                    continue

            self.eliminate_fail_action()

            send_publicly_result, send_publicly_fail_reason = self.chat_publicly()
            # send_publicly_result = True
            # send_publicly_fail_reason = ""

            send_privately_result, send_privately_fail_reason = self.chat_privately()
            if send_privately_result is None:
                log_print("该主播已发送过私信，跳过")
                self.switch_live_room(self._process)
                continue

            if send_publicly_result:
                send_result = True

            if send_publicly_fail_reason:
                fail_reason = send_publicly_fail_reason

            if send_privately_result:
                send_result = True

            if send_privately_fail_reason:
                fail_reason = send_privately_fail_reason

            if send_result:
                self._success_count += 1
            else:
                fail += 1
                # 统计失败原因
                if fail_reason:
                    if fail_reason in self._fail_reason_dict:
                        self._fail_reason_dict[fail_reason] += 1
                    else:
                        self._fail_reason_dict[fail_reason] = 1
            log_print(
                f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {fail}, 失败原因: {fail_reason}")
            log_print(
                f"公屏发送结果 {send_publicly_result} 失败原因: {send_publicly_fail_reason}")
            log_print(
                f"私信发送结果 {send_privately_result} 失败原因: {send_privately_fail_reason}")
            self.do_process(self._process + 1)
            time.sleep(self.action_short_dur)

            # 9. 向上滑动切换下一个主播
            self.switch_live_room(self._process)

    # 公屏聊天

    def chat_publicly(self) -> tuple[bool, str]:

        if not self.app_config.text_publicly:
            return False, ""

        d = self.d
        send_result = False
        fail_reason = ""

        # 1. 点击公屏按钮
        public_btn = d(resourceId="com.kugou.fanxing:id/dh_")
        if self.retry_exists(public_btn, "公屏按钮"):
            log_print("找到公屏按钮，点击公屏按钮")
            self.safe_click(public_btn, "公屏按钮")
            time.sleep(self.action_normal_dur)

            # 2. 输入文案并发送
            input_box = d(resourceId="com.kugou.fanxing:id/c5h")
            if self.retry_exists(input_box, "公屏输入框"):
                log_print("找到输入框，输入文案")
                self.safe_set_text(
                    input_box, self.app_config.text_publicly, "公屏输入框")
                time.sleep(self.action_short_dur)

                # 3. 点击发送按钮
                send_btn = d(resourceId="com.kugou.fanxing:id/c5n")
                if self.retry_exists(send_btn, "公屏发送按钮"):
                    log_print("找到公屏发送按钮，点击发送按钮")
                    self.safe_click(send_btn, "公屏发送按钮")
                    send_result = True
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到发送按钮")
                    fail_reason = "未找到发送按钮"
                    time.sleep(self.action_short_dur)

                d.click(0.3, 0.3)
                print("点击屏幕其他区域(头像)，收起键盘")
                time.sleep(self.action_normal_dur)
            else:
                log_print("未找到输入框")
                fail_reason = "未找到输入框"

        else:
            log_print("未找到公屏按钮")
            fail_reason = "未找到公屏按钮"

        return send_result, fail_reason

    # 私信聊天
    def chat_privately(self) -> tuple[bool, str]:
        d = self.d
        send_result = False
        fail_reason = ""
        has_sent = False

        # 1. 点击主播头像
        avatar = d(resourceId="com.kugou.fanxing:id/gmi")
        if self.retry_exists(avatar, "主播头像"):
            log_print("找到主播头像，点击主播头像")
            self.safe_click(avatar, "主播头像")
            time.sleep(self.action_short_dur)

            # 主播卡片弹出

            # 查找昵称
            nick_name_view = self.d(resourceId="com.kugou.fanxing:id/dk3")
            nick_name = "unknown"
            if nick_name_view.exists:
                nick_name = nick_name_view.get_text()

            self.follow(nick_name)

            # 2. 点击私信按钮
            msg_btn = d(resourceId="com.kugou.fanxing:id/djz")
            if self.retry_exists(msg_btn, "私信按钮"):
                log_print("找到私信按钮，点击私信按钮")
                self.safe_click(msg_btn, "私信按钮")
                time.sleep(self.action_normal_dur)

                # 3. 检查是否已发过相同文案
                chat_list = d(resourceId="com.kugou.fanxing:id/fgq")
                if self.retry_exists(chat_list, "聊天列表"):
                    idx = 0
                    while True:
                        item = chat_list.child(index=idx)
                        if not item.exists:
                            break
                        textview = item.child(
                            resourceId="com.kugou.fanxing:id/acn")
                        if textview.exists and textview.get_text() == self.text:
                            error_icon = item.child(
                                resourceId="com.kugou.fanxing:id/ca1")
                            if error_icon.exists:
                                log_print("找到相同文案，已发送过，但有发送失败图标，发送失败")
                            else:
                                log_print("找到相同文案，已发送过")
                                has_sent = True
                                fail_reason = "已经发送过文案"
                                break
                        idx += 1

                if not has_sent:
                    # 4. 输入文案并发送
                    input_box = d(resourceId="com.kugou.fanxing:id/c4x")

                    if self.retry_exists(input_box, "私聊输入框"):
                        log_print("找到输入框和发送按钮，输入文案")
                        self.safe_set_text(input_box, self.text, "私聊输入框")
                        time.sleep(self.action_short_dur)
                        send_btn = d.xpath('//*[@text="发送"]')
                        if self.retry_exists(send_btn, "私聊发送按钮"):
                            log_print("点击发送按钮发送私聊")
                            self.safe_click(send_btn, "私聊发送按钮")
                            time.sleep(self.action_normal_dur)
                        else:
                            log_print("未找到发送按钮")
                            fail_reason = "未找到发送按钮"

                        # 5. 点击其他区域移除焦点，收起键盘
                        log_print("点击其他区域移除焦点，收起键盘")
                        self.d.click(0.5, 0.25)
                        time.sleep(self.action_normal_dur)

                        # 6. 检测是否发送成功
                        if chat_list.exists:
                            check_send_msg_index = 0
                            while True:
                                item = chat_list.child(
                                    index=check_send_msg_index)
                                if not item.exists:
                                    fail_reason = "发送失败，未在聊天列表中找到发送的文案"
                                    break
                                textview = item.child(
                                    resourceId="com.kugou.fanxing:id/acn")
                                if textview.exists and textview.get_text() == self.text:
                                    error_icon = item.child(
                                        resourceId="com.kugou.fanxing:id/ca1")
                                    if error_icon.exists:
                                        log_print("找到发送的文案，发送失败，找到发送文案失败图标")
                                        fail_reason = "发送失败，找到发送文案失败图标"
                                        break
                                    log_print("找到发送的文案，发送成功")
                                    send_result = True
                                    break
                                check_send_msg_index += 1
                    else:
                        log_print("未找到私聊输入框")
                        fail_reason = "未找到私聊输入框"
                        time.sleep(self.action_short_dur)

                # 7. 关闭聊天窗口
                close_btn = d(resourceId="com.kugou.fanxing:id/h23")
                if self.retry_exists(close_btn, "关闭聊天窗口按钮"):
                    log_print("找到关闭聊天窗口按钮，点击关闭聊天窗口按钮")
                    self.safe_click(close_btn, "关闭聊天窗口按钮")
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到关闭聊天窗口按钮")

            else:
                log_print("未找到私信按钮")
                fail_reason = "未找到私信按钮"
                time.sleep(self.action_short_dur)

            # 8. 返回关闭主播卡片
            log_print("返回关闭主播卡片")
            d.press("back")
            time.sleep(self.action_short_dur)
        else:
            log_print("未找到主播头像")
            fail_reason = "未找到主播头像"
            time.sleep(self.action_short_dur)

        if has_sent:
            return None, None
        else:
            return send_result, fail_reason

    def follow(self, nick_name: str):
        follow_btn = self.d(resourceId="com.kugou.fanxing:id/djd")
        if self.retry_exists(follow_btn, "查找关注按钮"):
            follow_text_view = follow_btn.child(className="android.widget.TextView")
            follow_text = "关注"
            if follow_text_view.exists:
                follow_text = follow_text_view.get_text()
            if follow_text == "关注":
                log_print(f"找到关注按钮，点击关注 {nick_name}")
                self.safe_click(follow_btn)
                time.sleep(self.action_normal_dur)
            else:
                log_print(f"找到关注按钮，检测到已关注 {nick_name}")

    def switch_live_room(self, index: int):
        width, height = self.d.window_size()
        max_attempts = 3
        for attempt in range(max_attempts):
            # 每次滑动参数有微调
            x = width * (0.8 + 0.01 * attempt)
            x_end = width * (0.8 + 0.02 * attempt)
            y_start = height * (0.9 - 0.02 * attempt)
            y_end = height * (0.2 + 0.02 * attempt)
            # 不知道为什么水平滑一下后再触发第三次滑动，即再次垂直滑动时可以成功滑动
            if (attempt == 1):
                x = width * (0.2 + 0.01 * attempt)
                x_end = width * (0.8 + 0.01 * attempt)
                y_start = height * (0.4 - 0.02 * attempt)
                y_end = height * (0.4 + 0.02 * attempt)
            log_print(
                f"向上滑动切换下一个主播: {index + 1}/{self._total_process} attempt:{attempt+1} from:{x},{y_start} to:{x_end},{y_end}")
            self.d.swipe(x, y_start, x_end, y_end, 0.1)
            time.sleep(self.action_normal_dur)
            # 检查主播昵称
            anchor_name_view = self.d(resourceId="com.kugou.fanxing:id/glz")
            anchor_nanme = None
            if anchor_name_view.exists:
                try:
                    anchor_nanme = anchor_name_view.get_text()
                except Exception:
                    anchor_nanme = None
                if anchor_nanme is not None and anchor_nanme not in self.anchor_name_list:
                    log_print(f"主播昵称已变化，切换成功, 昵称为：{anchor_nanme}")
                    return
            log_print(f"主播昵称未变化，重试滑动，昵称为：{anchor_nanme}")
        log_print("多次滑动后主播昵称依然未变化，可能切换失败")
        self.check_when_retry("切换直播间失败")

    def on_hit_fail_action(self, fail_reason: str, count: int):
        if count > 5:
            log_print(f"多次连续命中失败原因{fail_reason}，终止直播间任务")
            raise TerminatedMonkeyError(
                101, f"多次连续命中失败原因{fail_reason}，终止直播间任务")
        super().on_hit_fail_action(fail_reason, count)
