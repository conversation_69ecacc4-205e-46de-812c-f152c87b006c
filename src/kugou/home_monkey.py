import time
from BaseMonkey import ActivityMonkey
from kugou.kugou_dialog import KugouDialog
from kugou.room_monkey import RoomMonkey
from models.monkey_context import MonkeyContext
from monkey_config import AppMonkeyConfig
from util.logger import log_print


class HomeMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig, room_monkey: RoomMonkey):
        # 酷狗直播首页Activity
        super().__init__(context, app_config, ".modul.mainframe.ui.MainFrameActivity")
        self.room = room_monkey
        self.dialog_monkey = KugouDialog(context)

    def run(self):
        self.run_loop(-1)

    def run_loop(self, deep: int):
        if deep > 3:
            return
        deep += 1
        log_print(f"开始运行酷狗首页 loop: {deep}")
        d = self.d
        time.sleep(self.action_short_dur)
        self.dialog_monkey.try_dismiss()

        log_print("开始切换到推荐tab")
        # 切换到推荐tab
        log_print("获取recommend_tab")
        recommend_tab = d.xpath('//*[@text="推荐"]')
        if self.retry_exists(recommend_tab, "recommend_tab"):
            log_print("找到推荐tab，点击推荐tab")
            self.safe_click(recommend_tab, "推荐Tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到推荐Tab")

        self.dialog_monkey.try_dismiss()
        # 进入第一个直播
        rv = d(resourceId="com.kugou.fanxing:id/fgq")
        if self.retry_exists(rv, "推荐列表"):
            items = rv.child(resourceId="com.kugou.fanxing:id/j4a")
            if self.retry_exists(items, "推荐列表item"):
                item_count = items.count
                if item_count > 0:
                    for i in range(item_count):
                        if self.room.get_process() >= self.room.get_total_process():
                            log_print("进度已完成..")
                            break
                        item = items[i]
                        if self.retry_exists(item, "推荐列表item"):
                            log_print(f"找到第{i+1}个item，点击第{i+1}个item")
                            self.safe_click(item, f"推荐列表item{i+1}")
                            time.sleep(self.action_normal_dur)
                            self.room.run()
                        else:
                            log_print(f"未找到第{i+1}个item ??")
                else:
                    log_print("推荐列表item为空")

            else:
                log_print("未找到直播豆腐块，尝试再找一次")
                time.sleep(self.action_short_dur)
                self.run_loop(deep)

        else:
            log_print("未找到推荐列表，尝试再找一次")
            time.sleep(self.action_short_dur)
            self.run_loop(deep)
