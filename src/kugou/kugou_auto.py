import time
from BaseMonkey import AppMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from kugou.home_monkey import HomeMonkey
from kugou.room_monkey import RoomMonkey
from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext

# 酷狗直播自动私聊主播脚本


class AutoKugou(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = RoomMonkey(context, app_config)
        self.home_monkey = HomeMonkey(context, app_config, self.room_monkey)
        self.proxy_monkey = self.room_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self):
        return "com.kugou.fanxing"

    def run(self):
        if self.version != "6.98.60":
            raise TerminatedMonkeyError.create_terminated_app_run_version_err(
                f"版本异常-检查是否是6.98.60，当前版本：{self.version}")
        loop_count = 0
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            if activity == '.modul.mainframe.ui.UpdateActivity':
                log_print("找到升级弹窗，点击返回键关闭")
                self.d.press("back")
                time.sleep(self.action_normal_dur)
                continue
            else:
                error_msg = f"等待酷狗对应的Activity，当前Activity: {activity}"
                log_print(f"等待酷狗对应的Activity，当前Activity: {activity}")
                time.sleep(self.action_normal_dur)
                if loop_count > 15:
                    raise TerminatedMonkeyError.create_terminated_app_run(
                        error_msg)
                loop_count += 1
