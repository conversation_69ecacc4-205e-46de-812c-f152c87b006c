import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


class KugouDialog(BaseDialog):

    # 关闭升级弹窗
    def close_upgrade_dialog(self):
        upgrade_dialog = self.d(text="稍后更新")
        if upgrade_dialog.exists:
            log_print("找到升级弹窗，点击稍后更新")
            upgrade_dialog.click()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # 关闭直播间未知弹窗
    def close_room_unknown_dialog(self):
        room_dialog = self.d(resourceId="com.kugou.fanxing:id/al9")
        if room_dialog.exists:
            log_print("找到直播间弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_young_mode_dialog(self):
        young_mode_dialog = self.d.xpath('//*[@text="未成年人模式"]')
        if young_mode_dialog.exists:
            log_print("找到青少年模式弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_phone_verify(self):
        phone_verify = self.d.xpath('//*[@text="酷狗安全验证"]')
        if phone_verify.exists:
            log_print("找到手机验证弹窗，点击返回关闭")
            raise TerminatedMonkeyError.create_terminated_app_run(
                "账号异常-手机验证弹窗")
        else:
            return False

    def close_forbidden_join_room_dialog(self):
        forbidden_join_room_dialog = self.d.xpath('//*[@text="抱歉，您已被禁止进入该房间"]')
        if forbidden_join_room_dialog.exists:
            log_print("找到被管理员踢出房间弹窗，点击确定")
            self.d.xpath('//*[@text="确定"]').click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_upgrade_dialog():
            result = True
        if self.close_room_unknown_dialog():
            result = True
        if self.close_young_mode_dialog():
            result = True
        if self.close_phone_verify():
            result = True
        if self.close_forbidden_join_room_dialog():
            result = True
        return result
