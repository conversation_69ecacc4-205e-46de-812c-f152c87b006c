# 酷狗直播自动私聊脚本

## 功能说明
这个脚本可以自动进入酷狗直播APP，在推荐页面选择主播，进入直播间后私聊主播发送指定文案。

## 主要功能
1. 自动关闭升级弹窗
2. 自动切换到推荐tab
3. 自动进入第一个直播间
4. 自动点击主播头像打开卡片
5. 自动点击私信按钮
6. 自动检查是否已发送过相同文案
7. 自动输入并发送文案
8. 自动关闭聊天窗口和主播卡片
9. 自动滑动切换到下一个主播
10. 重复执行指定次数

## 文件结构
- `kugou_auto.py` - 主入口类，负责启动APP和路由到不同页面
- `home_monkey.py` - 首页处理类，负责处理首页逻辑
- `room_monkey.py` - 直播间处理类，负责处理直播间私聊逻辑
- `kugou_dialog.py` - 弹窗处理类，负责关闭各种弹窗

## 使用方法

### 1. 基本使用
```python
import uiautomator2 as u2
from monkey_config import AppMonkeyConfig
from kugou.kugou_auto import AutoKugou

# 连接设备
d = u2.connect()

# 配置参数
config = AppMonkeyConfig(
    name="酷狗直播",
    text="你好主播，我是你的粉丝！",  # 要发送的文案
    count=10  # 发送次数
)

# 创建自动脚本实例并运行
auto_kugou = AutoKugou(d, config)
auto_kugou.run()
```

### 2. 运行示例脚本
```bash
cd src
python run_kugou.py
```

## 配置参数
- `name`: 应用名称
- `text`: 要发送的私聊文案
- `count`: 发送次数（循环次数）

## 注意事项
1. 确保设备已连接并安装了酷狗直播APP
2. 确保APP已登录账号
3. 脚本会自动处理各种弹窗
4. 如果找不到某个控件，脚本会记录日志并继续执行
5. 建议在测试环境中先运行少量次数

## 控件ID说明
- 升级弹窗: `text="稍后更新"`
- 推荐tab容器: `com.kugou.fanxing:id/el4`
- 推荐tab: `text="推荐"`
- 推荐列表: `com.kugou.fanxing:id/ff7`
- 直播豆腐块: `com.kugou.fanxing:id/j2a`
- 主播头像: `com.kugou.fanxing:id/gku`
- 私信按钮: `com.kugou.fanxing:id/djb`
- 聊天列表: `com.kugou.fanxing:id/ff7`
- 聊天文案: `com.kugou.fanxing:id/acj`
- 输入框: `com.kugou.fanxing:id/c4r`
- 发送按钮: `com.kugou.fanxing:id/c50`
- 移除焦点区域: `com.kugou.fanxing:id/f71`
- 关闭聊天按钮: `com.kugou.fanxing:id/h0e`
- 直播间弹窗: `com.kugou.fanxing:id/al9` 