import time
from BaseMonkey import ActivityMonkey
from kuaishou.kuaishou_dialog import KuaishouDialog
from util.logger import log_print


class KuaishouRoomMonkey(ActivityMonkey):
    def __init__(self, context, app_config, detail_monkey):
        # 直播间Activity - 需要根据实际的快手应用Activity修改
        super().__init__(context, app_config,
                         "com.kuaishou.live.core.basic.activity.LiveSlideActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly
        self.anchor_name = None
        self.dialog_monkey = KuaishouDialog(context)
        self.detail_monkey = detail_monkey

    def run(self):
        fail = 0
        log_print(f"进入直播间")
        while self.get_process() < self.get_total_process():
            time.sleep(self.action_normal_dur)
            self.dialog_monkey.try_dismiss()

            # 检测现在的直播间主播名字
            anchor_name_view = self.d(
                resourceId="com.smile.gifmaker:id/live_name_text")
            if self.retry_exists(anchor_name_view, "anchor_name_view"):
                cur_anchor_name = anchor_name_view.get_text()
                log_print(f"当前直播间主播名字: {cur_anchor_name}")
                if self.anchor_name and cur_anchor_name == self.anchor_name:
                    log_print(f"当前直播间主播名字与上次相同，跳过")
                    self.switch_live_room(self.get_process())
                    continue
                else:
                    log_print(f"当前直播间主播名字与上次不同，继续")
                    self.anchor_name = cur_anchor_name
            else:
                log_print("未找到主播名字")
                time.sleep(self.action_short_dur)
                # 尝试点退后按钮
                self.d.press("back")
                time.sleep(self.action_short_dur)
                continue

            send_result = False
            fail_reason = ""
           
            # 私聊
            send_privately_result, send_privately_fail_reason = self.chat_privately()

             # 暂时先不发公屏
            # send_publicly_result, send_publicly_fail_reason = self.chat_publicly()

            # if send_publicly_result:
            #     send_result = True
            # if send_publicly_fail_reason:
            #     fail_reason = send_publicly_fail_reason
            if send_privately_result:
                send_result = True
            if send_privately_fail_reason:
                fail_reason = send_privately_fail_reason

            if send_result:
                self._success_count += 1
            else:
                fail += 1
                if fail_reason:
                    if fail_reason in self.get_fail_reason_dict():
                        self.get_fail_reason_dict()[fail_reason] += 1
                    else:
                        self.get_fail_reason_dict()[fail_reason] = 1
            self.do_process(self.get_process() + 1)
            log_print(
                f"发送结果 {send_result}: 成功: {self.get_success_count()}, 失败: {fail}, 失败原因: {fail_reason}")
            # log_print(
            #     f"公屏发送结果 {send_publicly_result} 失败原因: {send_publicly_fail_reason}")
            log_print(
                f"私信发送结果 {send_privately_result} 失败原因: {send_privately_fail_reason}")
            time.sleep(self.action_short_dur)
            self.checkAndDissmissDialog()
            self.switch_live_room(self.get_process())

    def chat_publicly(self) -> tuple:
        if not self.text_publicly:
            return False, ""

        d = self.d
        send_result = False
        fail_reason = ""
            
        return send_result, fail_reason

    def chat_privately(self) -> tuple:
        if not self.text:
            return False, ""

        d = self.d
        send_result = False
        fail_reason = ""
        
        # 1. 点击主播头像
        avatar = d(resourceId="com.smile.gifmaker:id/live_name_text")
        if self.retry_exists(avatar, "avatar"):
            self.safe_click(avatar, "avatar")
            time.sleep(self.action_normal_dur)

            # 2. 点击主页按钮
            detail_btn = d.xpath('//*[@text="主页"]')
            # detail_btn = d(resourceId="com.smile.gifmaker:id/live_audience_new_profile_detailed")
            if self.retry_exists(detail_btn, "detail_btn"):
                self.safe_click(detail_btn, "detail_btn")
                time.sleep(self.action_normal_dur)
                # 进入个人主页
                reult, detail_fail_reason = self.detail_monkey.run()
                if reult:
                    send_result = True
                else:
                    fail_reason = detail_fail_reason
            else:
                fail_reason = "未找到主页按钮"  
        else:
            fail_reason = "未找到主播头像"
        return send_result, fail_reason
    
    def checkAndDissmissDialog(self):
        # 关闭个人页窗口
        detail_btn = self.d.xpath('//*[@text="主页"]')
        if detail_btn.exists:
            log_print("主播个人卡片弹窗还在，点击关闭")
            self.d.press("back") 
        else:
            log_print("未找到主播个人卡片弹窗")
        time.sleep(self.action_short_dur)

    def switch_live_room(self, index: int):
        width, height = self.d.window_size()
        max_attempts = 4
        for attempt in range(max_attempts):
            # 每次滑动参数有微调
            x = width * (0.6 + 0.01 * attempt)
            x_end = width * (0.6 + 0.02 * attempt)
            y_start = height * (0.4 + attempt / max_attempts)
            y_end = height * (0.4 + attempt / max_attempts -
                              0.2 * (1 + attempt / max_attempts))
            log_print(
                f"向上滑动切换下一个主播: {index + 1}/{self.app_config.count} attempt:{attempt+1} from:{x},{y_start} to:{x_end},{y_end}")
            self.d.swipe(x, y_start, x_end, y_end, 0.05)
            time.sleep(self.action_normal_dur)
            # 检查主播昵称
            anchor_name_view = self.d(
                resourceId="com.smile.gifmaker:id/live_name_text")
            anchor_name = None
            if anchor_name_view.exists:
                try:
                    anchor_name = anchor_name_view.get_text()
                except Exception:
                    anchor_name = None
                if anchor_name is not None and anchor_name != self.anchor_name:
                    log_print(f"主播昵称已变化，切换成功, 昵称为：{anchor_name}")
                    return
            log_print(f"主播昵称未变化，重试滑动，昵称为：{anchor_name}")
        log_print("多次滑动后主播昵称依然未变化，可能切换失败")