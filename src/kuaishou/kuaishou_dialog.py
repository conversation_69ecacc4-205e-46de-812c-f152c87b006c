import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


class KuaishouDialog(BaseDialog):

    def close_young_mode(self):
        # 关闭青少年模式弹窗 - 需要根据实际的快手UI修改
        young_mode = self.d.xpath('//*[@text="青少年模式"]')
        if young_mode.exists:
            log_print("找到青少年模式弹窗，点击我知道了")
            young_mode_known = self.d.xpath('//*[@text="我知道了"]')
            if young_mode_known.exists:
                young_mode_known.click()
                time.sleep(self.action_normal_dur)
            return True
        else:
            return False
        
    def close_tools_dialog(self):
        # 关闭工具栏弹窗
        tools_dialog = self.d.xpath('//*[@text="添加到主屏幕"]')
        if tools_dialog.exists:
            log_print("找到工具栏弹窗，点击关闭")
            cancel_btn = self.d.xpath('//*[@text="取消"]')
            if cancel_btn.exists:
                cancel_btn.click()
            else:
                log_print("未找到取消按钮")
                return False
            time.sleep(self.action_normal_dur)
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_young_mode():
            result = True
        if self.close_tools_dialog():
            result = True
        return result 