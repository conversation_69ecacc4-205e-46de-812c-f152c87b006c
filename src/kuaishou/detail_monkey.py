import time
from BaseMonkey import ActivityMonkey
from kuaishou.kuaishou_dialog import KuaishouDialog
from util.logger import log_print

# 个人主页
# 主播页面：关注按钮，私聊按钮
class KuaishouDetailMonkey(ActivityMonkey):
    def __init__(self, context, app_config, private_monkey):
        super().__init__(context, app_config, "com.yxcorp.gifshow.profile.activity.UserProfileActivity")
        self.context = context
        self.dialog_monkey = KuaishouDialog(context)
        self.anchor_name = None
        self.private_monkey = private_monkey

    def run(self) -> tuple:
        send_result = False
        fail_reason = ""
        self.dialog_monkey.try_dismiss()
        detail_anchor_name = self.d(
                resourceId="com.smile.gifmaker:id/user_name_tv")
        if self.retry_exists(detail_anchor_name, "detail_anchor_name"):
            if self.anchor_name and self.anchor_name == detail_anchor_name.get_text():
                log_print("当前主播名字与上次相同")
            else:
                self.anchor_name = detail_anchor_name.get_text()
                log_print("当前主播名字: %s", self.anchor_name)
        else:
            log_print("未找到主播名字")

        # 关注按钮
        follow_btn = self.d.xpath('//*[@text="i 关注"]')
        if follow_btn.exists:
            self.safe_click(follow_btn, "follow_btn")
            log_print("点击关注按钮 anchor_name: %s", self.anchor_name)
            time.sleep(self.action_normal_dur)

        # 私聊按钮
        private_btn = self.d(resourceId="com.smile.gifmaker:id/send_message")
        if self.retry_exists(private_btn, "private_btn"):
            self.safe_click(private_btn, "private_btn")
            time.sleep(self.action_normal_dur)
            result, fail_reason = self.private_monkey.run()
            self.close_detail_activity()
            if result:
                send_result = True
            else:
                fail_reason = fail_reason
        else:
            fail_reason = "未找到私聊按钮"
        
        return send_result, fail_reason
    
    def close_detail_activity(self):
        # 关闭私聊窗口
        close_btn = self.d(resourceId="com.smile.gifmaker:id/left_btn")
        if close_btn.exists:
            log_print("找到关闭个人页面按钮，点击关闭")
            self.safe_click(close_btn, "close_private_chat")
        else:
            log_print("找到关闭个人页面按钮，点击返回")
            self.d.press("back")
        time.sleep(self.action_short_dur)
        
