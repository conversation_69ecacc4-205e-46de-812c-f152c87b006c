import time
from BaseMonkey import ActivityMonkey
from kuaishou.kuaishou_dialog import KuaishouDialog
from util.logger import log_print


class KuaishouHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config, room_monkey):
        # 首页Activity
        super().__init__(context, app_config, "com.yxcorp.gifshow.HomeActivity")
        self.room_monkey = room_monkey
        self.dialog_monkey = KuaishouDialog(context)

    def run(self):
        return self.run_loop(0)

    def run_loop(self, deep: int):
        if deep > 3:
            log_print("循环次数超过3次，退出")
            return False
        deep += 1
        d = self.d
        self.dialog_monkey.try_dismiss()
        
        # 切换到底部首页tab
        home_tab = d.xpath('//*[@text="首页"]')
        if self.retry_exists(home_tab, "home_tab"):
            log_print("点击底部首页tab")
            self.safe_click(home_tab, "home_tab")
            time.sleep(5)
        else:
            log_print("未找到首页tab，等待循环执行")
            time.sleep(5)
            return self.run_loop(deep)
        
         # 切换到顶部直播tab
        live_tab = d.xpath('//*[@content-desc="直播"]')
        if self.retry_exists(live_tab, "live_tab"):
            log_print("点击顶部直播tab")
            self.safe_click(live_tab, "live_tab")
            time.sleep(5)
        else:
            log_print("未找到直播tab，等待循环执行")
            time.sleep(5)
            return self.run_loop(deep)

        # 查找直播列表
        rv = d(resourceId="com.smile.gifmaker:id/live_explore_fragment_recycler_view")
        if not rv.exists:
            log_print("未找到直播列表，等待循环执行")
            return self.run_loop(deep)
            
        # 查找第一个直播间
        first_room = rv.child(resourceId="com.smile.gifmaker:id/live_explore_item_auto_play_container")
        if not first_room.exists:
            log_print("未找到直播房间，等待循环执行")
            return self.run_loop(deep)
            
        self.safe_click(first_room, "first_room")
        time.sleep(self.action_long_dur)

        # 直播间内操作
        self.room_monkey.run()
        return True 