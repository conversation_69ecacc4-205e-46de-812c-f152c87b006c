#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import sys
import os
# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import uiautomator2 as u2
from monkey_config import AppMonkeyConfig
from kuaishou.kuaishou_auto import KuaishouAuto
from models.monkey_context import MonkeyContext
from util.logger import log_print

def run_kuaishou():
    """运行快手自动脚本示例"""
    try:
        # 连接设备
        log_print("正在连接设备...")
        d = u2.connect()
        log_print(f"设备连接成功: {d.serial}")
        
        # 创建上下文
        context = MonkeyContext()
        context.set_ui_automator_device(d)
        
        # 配置参数
        config = AppMonkeyConfig(
            name="快手",
            text="""1绿泡泡视频通话美颜-【秒颜】
        可以在通话时用到【丰富妆容和滤镜、头饰】，
        能让您视频通话和现在一样美美嘟红色书本搜同名了解更多 
        【复制本条】免fei体验，邀请马SFdSO""",  # 私聊文案
            text_publicly="主播好棒！",  # 公屏文案
            count=5  # 发送次数
        )
        
        # 创建自动脚本实例
        log_print("创建快手自动脚本实例...")
        auto_kuaishou = KuaishouAuto(context, config)
        
        # 运行脚本
        log_print("开始运行快手自动脚本...")
        auto_kuaishou.run()
        
        log_print("快手自动脚本运行完成！")
        
    except Exception as e:
        log_print(f"运行快手脚本时发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_kuaishou() 