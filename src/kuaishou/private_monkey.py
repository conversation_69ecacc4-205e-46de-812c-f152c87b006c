import time
from BaseMonkey import ActivityMonkey
from kuaishou.kuaishou_dialog import KuaishouDialog
from util.logger import log_print


# 私聊页面
# 存在主播关注、相互关注才能私聊的情况
# 私聊发送一次后，在对方回复或关注你之前，每天最多只能发一条私信
class KuaishouPrivateMonkey(ActivityMonkey):
    def __init__(self, context, app_config):
        super().__init__(context, app_config, "com.yxcorp.gifshow.message.imchat.acivity.IMChatActivity")
        self.dialog_monkey = KuaishouDialog(context)

    def run(self) -> tuple:
        send_result = False
        fail_reason = ""
        d = self.d
        self.dialog_monkey.try_dismiss()
        # 1.检查是否已发过相同文案
        chat_list = d(resourceId="com.smile.gifmaker:id/recycler_view")
        has_sent = False
        if chat_list.exists:
            idx = 0
            while True:
                item = chat_list.child(index=idx)
                if not item.exists:
                    break
                textview = item.child(
                    resourceId="com.smile.gifmaker:id/message")
                if textview.exists and textview.get_text() == self.app_config.text:
                    log_print("找到相同文案，已发送过")
                    has_sent = True
                    fail_reason = "已经发送过文案"
                    break
                idx += 1

        if not has_sent:
            log_print("未找到相同文案，发送私聊")
            # 4. 输入文案并发送
            input_box = d(resourceId="com.smile.gifmaker:id/editor")
            # 主播可能设置相互关注才能私聊
            if input_box.exists:
                self.safe_set_text(input_box, self.app_config.text, "private_input_box")
                time.sleep(self.action_short_dur)
                
                send_btn = d(resourceId="com.smile.gifmaker:id/send_btn")
                if self.retry_exists(send_btn, "private_send_btn"):
                    if send_btn.info["enabled"]:
                        self.safe_click(send_btn, "private_send_btn")
                        time.sleep(self.action_normal_dur)
                        send_result = True
                        log_print("私聊消息发送成功")
                    else:
                        log_print("私聊发送按钮不可用")
                        fail_reason = "私聊发送按钮不可用"
                else:
                    log_print("未找到私聊发送按钮")
                    fail_reason = "未找到私聊发送按钮"
            else:
                log_print("未找到私聊输入框")
                fail_reason = "未找到私聊输入框"
                
            # 关闭私聊窗口
            self.close_private_chat()
        else:
            # 关闭私聊窗口
            self.close_private_chat()
        return send_result, fail_reason
            
    def close_private_chat(self):
        # 关闭私聊窗口
        close_btn = self.d(resourceId="com.smile.gifmaker:id/navi_normal_left")
        if close_btn.exists:
            log_print("找到关闭私聊窗口按钮，点击关闭")
            self.safe_click(close_btn, "close_private_chat")
        else:
            log_print("未找到关闭私聊窗口按钮，点击返回")
            self.d.press("back")
        time.sleep(self.action_short_dur)
