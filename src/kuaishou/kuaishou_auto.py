import time
from BaseMonkey import <PERSON><PERSON><PERSON>onkey
from models.monkey_context import <PERSON><PERSON><PERSON><PERSON><PERSON>
from kuaishou.home_monkey import <PERSON>ais<PERSON>HomeMonkey
from kuaishou.room_monkey import Kuais<PERSON><PERSON>oomMonkey
from monkey_config import AppMonkeyConfig
from monkey_proxy_mixin import MonkeyProxyMixin
from kuaishou.detail_monkey import KuaishouDetailMonkey
from kuaishou.private_monkey import KuaishouPrivateMonkey
from util.logger import log_print

# 快手自动脚本
class KuaishouAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.private_monkey = KuaishouPrivateMonkey(context, app_config)
        self.detail_monkey = KuaishouDetailMonkey(context, app_config, self.private_monkey)
        self.room_monkey = KuaishouRoomMonkey(context, app_config, self.detail_monkey)
        self.home_monkey = <PERSON><PERSON><PERSON>HomeMonkey(
            context, app_config, self.room_monkey)
        self.proxy_monkey = self.room_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self):
        return "com.smile.gifmaker"

    def run(self):
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            if activity == self.detail_monkey.activity:
                self.detail_monkey.run()
                break
            if activity == self.private_monkey.activity:
                self.private_monkey.run()
                break
            else:
                log_print(f"等待快手对应的Activity，当前Activity: {activity}")
                time.sleep(self.action_normal_dur) 