import time
from BaseMonkey import <PERSON><PERSON><PERSON><PERSON>key, BaseAuto
from models.monkey_context import <PERSON><PERSON>ontex<PERSON>
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from monkey_proxy_mixin import Monkey<PERSON>roxyMixin
from util.logger import log_print
from yomi.yomi_home import Yo<PERSON><PERSON><PERSON>Monkey
from yomi.yomi_room import YomiRoomMonkey

# Yo语音 App 自动灌水脚本


class YomiAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = YomiRoomMonkey(context, app_config)
        self.home_monkey = YomiHomeMonkey(
            context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self):
        return "com.yy.yomi"

    def run(self):
        if self.version != "2.21.1":
            raise TerminatedMonkeyError.create_terminated_app_run_version_err(
                f"版本异常-未找到娱乐tab-检查是否是2.21.1，当前版本：{self.version}")
        # 启动赫兹App并进入首页
        loop_count = 0
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            else:
                if activity == "com.yy.dreamer.login.LoginActivity":
                    log_print("在启动等待中检查到现在在登录页，终止运行")
                    raise TerminatedMonkeyError.create_terminated_app_run_account_err("账号异常-未登录")
                error_msg = f"等待Yo语音对应的Activity，当前Activity: {activity}"
                log_print(f"{error_msg}")
                time.sleep(self.action_normal_dur)
                loop_count += 1
                if loop_count > 15:
                    raise TerminatedMonkeyError.create_terminated_app_run(
                        error_msg)

        log_print("Yo语音自动私聊任务完成！")
