import time
from BaseMonkey import ActivityMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import UiObject
from yomi.yomi_dialog import YomiDialog
from yomi.yomi_room import YomiRoomMonkey

# Yo语音首页脚本


class YomiHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config, room_monkey: YomiRoomMonkey):
        # 首页Activity
        super().__init__(context, app_config, "com.yy.dreamer.home.MainActivity")
        self.room_monkey = room_monkey
        self.dialog_monkey = YomiDialog(context)
        self.live_room_list = []
        self.rv_index = 0
        self.fail = 0
        self.retry_action = self.check_when_retry
        self.switch_and_can_not_find_count = 0

    def check_when_retry(self, tag: str, retry_count: int = 0):
        log_print(f"home check_when_retry {tag} {retry_count}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def run(self):
        try:
            self.run_home()
        except TerminatedMonkeyError as e:
            if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                log_print("收到完成信号，Yo语音任务完成")
            else:
                raise e

        return True

    def run_home(self):
        d = self.d
        # 1. 切换到底部娱乐tab
        entertainment_tab = d.xpath('//*[@resource-id="com.yy.yomi:id/acw"]')

        if self.retry_exists(entertainment_tab, "查找娱乐tab"):
            log_print("点击底部娱乐tab")
            self.safe_click(entertainment_tab, "点击娱乐tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到娱乐tab")

        # 2. 切换到“派对”tab
        party_tab = d.xpath('//*[@text="派对"]')
        if self.retry_exists(party_tab, "查找派对tab"):
            log_print("找到派对tab，点击")
            self.safe_click(party_tab, "点击派对tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到派对tab")

        # 2. 切换到“热门”tab
        hot_tab = d.xpath('//*[@text="热门"]')
        if self.retry_exists(hot_tab, "查找热门tab"):
            log_print("点击热门tab")
            self.safe_click(hot_tab, "点击热门tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到热门tab")

        self.process_room_list()

        return True

    def process_room_list(self):

        self.rv_index = 0

        while True:
            # YOMI语音私聊每天30次限制
            if self._process >= self._total_process or self._success_count >= 30:
                log_print("已完成进入次数..")
                break

            self.dialog_monkey.try_dismiss()

            rv = self.d(resourceId="com.yy.yomi:id/a_a")

            if not self.retry_exists(rv, "热门列表"):
                log_print("未找到热门列表 process_room_list 失败")
                break

            item = rv.child(index=self.rv_index)
            if not self.retry_exists(item, f"item: {self.rv_index}", 2):
                log_print(f"未找到item: {self.rv_index}，尝试滑动")
                self.switch_live_room(self.d)
                self.rv_index = 0
                continue

            find_room_result = self.find_room(item, self.rv_index)
            if not find_room_result:
                if self.switch_and_can_not_find_count > 5:
                    log_print(f"多次尝试滑动，仍未找到符合条件的item，可能已经滑到底，任务完成")
                    raise TerminatedMonkeyError.create_process_finished()
                log_print(f"未找到符合find_room条件的item: {self.rv_index}，尝试滑动")
                self.switch_live_room(self.d)
                self.rv_index = 0
                self.switch_and_can_not_find_count += 1
                continue

            time.sleep(self.action_normal_dur)
            self.switch_and_can_not_find_count = 0
            self.rv_index += 1

    def find_room(self, item: UiObject, i: int) -> bool:
        topic_name_view = item.child(resourceId="com.yy.yomi:id/a2y")
        if self.retry_exists(topic_name_view, f"topic_name_view: {i}是否存在"):
            topic_name = topic_name_view.get_text()
            log_print(f"找到第{i+1}个聊天室，主题：{topic_name}")
            # 判断topic_name是否在live_room_list中
            if topic_name in self.live_room_list:
                log_print(f"第{i+1}个聊天室主题：{topic_name} 已存在，跳过")
                return False
            self.live_room_list.append(topic_name)
            self.safe_click(topic_name_view, f"topic_name_view: {i}")
            log_print(f"点击聊天室：{topic_name}")
            time.sleep(self.action_normal_dur)
            if self.dialog_monkey.close_channel_forbidden_dialog():
                log_print(f"第{i+1}个聊天室主题：{topic_name} 禁止进入，跳过")
                return False
            self.dialog_monkey.try_dismiss()
            room_monkey = YomiRoomMonkey(self.context, self.app_config)
            time.sleep(self.action_long_dur)

            def progress_callback(progress: int, total_process: int):
                log_print(f"进度: {progress}/{total_process}")
                if progress <= 1:
                    return
                result_list = room_monkey.send_privately_result_list
                fail_reason_list = room_monkey.send_privately_fail_reason_list
                real_index = progress - 1 - 1
                send_result = result_list[real_index]
                fail_reason = fail_reason_list[real_index]
                if send_result:
                    self._success_count += 1
                else:
                    self.fail += 1
                    if fail_reason:
                        if fail_reason in self._fail_reason_dict:
                            self._fail_reason_dict[fail_reason] += 1
                        else:
                            self._fail_reason_dict[fail_reason] = 1
                log_print(
                    f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {self.fail}, 失败原因: {fail_reason}"
                )
                self.do_process(self._process + 1)
                if self._process >= self._total_process or self._success_count >= 30:
                    log_print("已完成进入次数，发送终止信号..")
                    raise TerminatedMonkeyError.create_process_finished()

            room_monkey.register_progress_callback(progress_callback)
            room_monkey.run()
            room_monkey.unregister_progress_callback(progress_callback)
            time.sleep(self.action_normal_dur)
            return True

        return False

    def has_more_tofu(self):
        """检查是否还有更多豆腐块可点击"""
        rv = self.d(resourceId="com.yy.yomi:id/a_a")
        if rv.exists:
            topic_name_view = rv.child(resourceId="com.yy.yomi:id/a2y")
            if topic_name_view.exists:
                topic_name = topic_name_view.get_text()
                if topic_name in self.live_room_list:
                    return False
                else:
                    return True
        return False

    def switch_live_room(self, d):
        """向上滑动切换直播间列表"""
        width, height = d.window_size()
        for attempt in range(3):
            x = width * (0.5 + 0.01 * attempt)
            y_start = height * (0.8 - 0.02 * attempt)
            y_end = height * (0.3 + 0.02 * attempt)
            log_print(
                f"向上滑动切换直播间列表 attempt:{attempt+1} from:{x},{y_start} to:{x},{y_end}"
            )
            d.swipe(x, y_start, x, y_end, 0.1)
            time.sleep(self.action_normal_dur)

            # 检查是否有新的豆腐块
            if self.has_more_tofu():
                log_print("滑动后找到新的豆腐块")
                break
        else:
            log_print("多次滑动后仍未找到新的豆腐块")
