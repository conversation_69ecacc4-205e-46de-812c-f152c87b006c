import time
from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print

# Yo语音弹窗处理


class YomiDialog(BaseDialog):

    # 青少年模式弹窗
    def close_young_mode_dialog(self):
        young_mode_dialog = self.d.xpath('//*[@text="进入青少年模式"]')
        if young_mode_dialog.exists:
            log_print("找到青少年模式弹窗，点击我知道了")
            confirm_btn = self.d(
                resourceId="com.yy.mobile.plugin.dreamerhomex:id/tv_confirm_button")
            confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # web弹窗
    def close_web_dialog(self):
        web_dialog = self.d(resourceId="com.yy.yomi:id/a3o")
        if web_dialog.exists:
            log_print("找到web弹窗，点击回退按钮")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_upgrade_dialog(self):
        upgrade_dialog = self.d.xpath('//*[@text="发现新版本"]')
        if upgrade_dialog.exists:
            log_print("找到升级弹窗，点击关闭")
            close_btn = self.d(
                resourceId="com.yy.mobile.plugin.dreamerhomex:id/home_apk_upgrade_cancel")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def process_join_room_dialog(self):
        join_room_dialog = self.d.xpath('//*[@text="进入该房间的同时，将会关闭所在房间"]')
        if join_room_dialog.exists:
            log_print("找到加入房间弹窗，点击确定")
            dont_remind_cb = self.d(
                resourceId="com.yy.dreamer.plugin.basesdk:id/remind_check_box")
            dont_remind_cb.click_exists()
            time.sleep(self.action_short_dur)
            confirm_btn = self.d(resourceId="com.yy.yomi:id/ei")
            confirm_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_kickout_room_dialog(self):
        kickout_room_dialog = self.d.xpath('//*[@text="您已被管理员踢出房间"]')
        if kickout_room_dialog.exists:
            log_print("找到被管理员踢出房间弹窗，点击确定")
            confirm_btn = self.d(resourceId="com.yy.yomi:id/ei")
            confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # 退出麦序弹窗
    def leave_micro_dialog(self):
        leave_micro_dialog = self.d.xpath('//*[@text="您当前正在麦序上，是否退出?"]')
        if leave_micro_dialog.exists:
            log_print("找到 您当前正在麦序上，是否退出? 弹窗，点击退出")
            confirm_btn = self.d(resourceId="com.yy.yomi:id/ei")
            confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_account_security_dialog(self):
        """账号存在风险，暂时冻结弹窗"""
        account_security_dialog = self.d.xpath(
            '//*[@text="帐号存在安全风险-Y001 ]被系统暂时冻结保护。请登录YY安全中心修改密码激活登录。"]')
        if account_security_dialog.exists:
            log_print("找到账号安全弹窗，点击确定")
            # confirm_btn = self.d.xpath('//*[@text="确定"]')
            # confirm_btn.click_exists()
            time.sleep(self.action_normal_dur)
            raise TerminatedMonkeyError.create_terminated_app_run("账号异常")
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_young_mode_dialog():
            result = True
        if self.close_web_dialog():
            result = True
        if self.process_join_room_dialog():
            result = True
        if self.close_kickout_room_dialog():
            result = True
        if self.leave_micro_dialog():
            result = True
        if self.close_account_security_dialog():
            result = True
        return result
