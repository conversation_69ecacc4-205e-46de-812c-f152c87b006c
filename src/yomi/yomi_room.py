import time
from typing import List, <PERSON>ple
from BaseMonkey import ActivityMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from yomi.yomi_dialog import YomiDialog
from uiautomator2 import Device

IS_TEST = False


# Yo语音直播间脚本


class YomiRoomMonkey(ActivityMonkey):
    def __init__(self, context, app_config):
        # 直播间Activity
        super().__init__(context, app_config,
                         "com.duowan.mobile.basemedia.watchlive.activity.LiveTemplateActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly
        # self.action_short_dur = 3
        # self.action_normal_dur = 6
        # self.action_long_dur = 10
        self.dialog_monkey = YomiDialog(context)
        self.send_publicly_result = False
        self.send_publicly_fail_reason = ""
        self.send_privately_result_list: List[bool] = [False]
        self.send_privately_fail_reason_list: List[str] = [""]
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str, retry_count: int = 0):
        log_print(f"room check_when_retry {tag} {retry_count}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def check_activity_and_back(self, retry_count: int = 3, expand_fail: bool = True):
        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            elif activity == "com.yy.dreamer.home.MainActivity":
                log_print(f"当前activity: {activity}，是home，终止任务")
                raise TerminatedMonkeyError(101, "页面是首页，终止直播间任务")
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_normal_dur}秒"
                log_print(error_msg)
                time.sleep(self.action_normal_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back, None, None, retry_count, expand_fail)

    def on_hit_fail_action(self, fail_reason: str, count: int):
        if count > 5:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == "com.yy.dreamer.home.MainActivity" or activity == self.activity:
                log_print(f"多次连续命中失败原因{fail_reason}，终止直播间任务")
                raise TerminatedMonkeyError(101, "多次连续命中失败原因，终止直播间任务")
            else:
                log_print(f"多次连续命非法activity，重新start")
                self.d.app_start("com.yy.yomi")
        super().on_hit_fail_action(fail_reason, count)

    def run(self):
        try:
            self.run_room()
        except TerminatedMonkeyError as e:
            # 这里要区分home抛出的TerminatedMonkeyError create_process_finished异常
            if e.code == 101:
                log_print(f"页面是首页，终止直播间任务")
                return False
            else:
                raise e
        return True

    def run_room(self):
        if IS_TEST:
            time.sleep(4)
            print("IS_TEST直播间完成")
            self.send_publicly_result = True
            self.send_privately_result_list = [True] * 1
            self.send_privately_fail_reason_list = [""] * 1
            self._total_process = 2
            self.do_process(2)
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True

        d = self.d
        time.sleep(self.action_normal_dur)

        # 1. 公屏发送消息
        send_publicly_result, send_publicly_fail_reason = self.chat_publicly(d)
        self.send_publicly_result = send_publicly_result
        self.send_publicly_fail_reason = send_publicly_fail_reason
        self.do_process(1)

        # 2. 私聊所有用户头像
        self.chat_all_users_privately(d)

        log_print("直播间操作完成，退出直播间")
        self.d.press("back")
        time.sleep(self.action_normal_dur)

    def chat_publicly(self, d: Device):
        """公屏发送消息"""
        send_result = False
        fail_reason = ""

        if not self.text_publicly:
            return True, ""

        # 点击公屏发送入口
        chat_entry = d(
            resourceId="com.yy.mobile.lib.dreamerchannelx:id/send_msg_btn")
        if self.retry_exists(chat_entry, "公屏发送入口"):
            self.safe_click(chat_entry, "公屏发送入口")
            time.sleep(self.action_short_dur)

            # 输入内容
            input_box = d(
                resourceId="com.yy.dreamer.plugin.basesdk:id/content_et_pc_basic")
            if self.retry_exists(input_box, "公屏输入框"):
                self.safe_set_text(input_box, self.text_publicly, "公屏输入框")
                time.sleep(self.action_short_dur)

                # 点击发送
                send_btn = d(
                    resourceId="com.yy.dreamer.plugin.basesdk:id/send_tv")
                if self.retry_when_false(lambda: send_btn.exists):
                    self.safe_click(send_btn, "公屏发送按钮")
                    time.sleep(self.action_short_dur)
                    self.d.press("back")
                    send_result = True
                    log_print("公屏消息发送成功")
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"

        return send_result, fail_reason

    def chat_all_users_privately(self, d: Device):
        """私聊所有用户头像"""
        # 查找所有用户头像
        avatar_containers = d(
            resourceId="com.yy.mobile.lib.dreamerchannelx:id/iv_anchor_portrait")

        if not self.retry_exists(avatar_containers, "用户头像容器"):
            log_print("未找到用户头像容器")
            return

        # 获取所有头像的数量
        avatar_count = avatar_containers.count
        log_print(f"找到 {avatar_count} 个用户头像")
        self._total_process = avatar_count + 1  # +1是公屏
        self.send_privately_result_list = [False] * avatar_count
        self.send_privately_fail_reason_list = [""] * avatar_count

        # 遍历所有头像
        for i in range(avatar_count):
            try:
                # 获取第i个头像
                avatar = avatar_containers[i]
                if self.retry_exists(avatar, f"用户头像: {i}"):
                    log_print(f"点击第 {i + 1} 个用户头像")
                    self.safe_click(avatar, f"用户头像: {i}")
                    time.sleep(self.action_normal_dur)

                    # 弹出卡片后
                    # 获取昵称
                    user_nick_view = self.d(resourceId="com.yy.mobile.lib.dreamerchannelx:id/tv_channel_user_card_nick")
                    if self.retry_exists(user_nick_view, "查找用户昵称视图", 3):
                        nick_name = user_nick_view.get_text()
                        log_print(f"找到第{i + 1}个用户头像的昵称：{nick_name}")
                    else:
                        nick_name = "Unknown"
                        log_print(f"找不到第{i + 1}个用户头像的昵称！")

                    self.follow(nick_name)

                    # 执行私聊步骤
                    send_privately_result, send_privately_fail_reason = self.chat_single_user_privately(
                        d)
                    self.send_privately_result_list[i] = send_privately_result
                    self.send_privately_fail_reason_list[i] = send_privately_fail_reason

                    self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏
                else:
                    log_print(f"第 {i + 1} 个头像不存在")
                    break
            except Exception as e:
                if isinstance(e, TerminatedMonkeyError):
                    raise e
                else:
                    log_print(f"处理第 {i + 1} 个头像时出错: {str(e)}")
                    continue

    def follow(self, nick_name: str):
        follow_view = self.d(resourceId="com.yy.mobile.lib.dreamerchannelx:id/tv_focus_status")
        if self.retry_exists(follow_view, "查找关注按钮"):
            follow_status = follow_view.get_text()
            if follow_status == "关注":
                log_print(f"找到关注按钮，点击关注 {nick_name}")
                self.safe_click(follow_view)
                time.sleep(self.action_short_dur)
            elif follow_status == "已关注":
                log_print(f"已经关注了 {nick_name}")
            else:
                log_print(f"无法确定关注状态 {nick_name}")
        else:
            log_print(f"找不到关注按钮，无法关注 {nick_name}")

    def chat_single_user_privately(self, d: Device):
        """对单个用户执行私聊步骤"""
        send_result = False
        fail_reason = ""
        # 点击发消息按钮
        message_btn = d(
            resourceId="com.yy.mobile.lib.dreamerchannelx:id/tv_chat_status")
        if self.retry_exists(message_btn, "发消息按钮"):
            self.safe_click(message_btn, "发消息按钮")
            time.sleep(self.action_normal_dur)

            # 检查是否已发送过相同文案
            chat_rv = d(resourceId="com.yy.mobile.plugin.dreamerimx:id/list")
            has_sent = False
            if self.retry_exists(chat_rv, "聊天列表"):
                idx = 0
                while True:
                    item = chat_rv.child(index=idx)
                    if not item.exists:
                        break
                    textview = item.child(
                        resourceId="com.yy.mobile.plugin.dreamerimx:id/tv_chatcontent")
                    if textview.exists and textview.get_text() == self.text:
                        msg_status = item.child(
                            resourceId="com.yy.mobile.plugin.dreamerimx:id/msg_status")
                        if msg_status.exists:
                            log_print("已发送过相同私聊文案，但有消息状态红点，发送失败")
                        else:
                            has_sent = True
                            log_print("已发送过相同私聊文案")
                            break
                    idx += 1

            if not has_sent:
                # 输入私聊文案
                input_box = d(
                    resourceId="com.yy.mobile.plugin.dreamerimx:id/et_sendmessage")

                if self.retry_exists(input_box, "私聊输入框"):
                    self.safe_set_text(input_box, self.text, "私聊输入框")
                    time.sleep(self.action_short_dur)
                    send_btn = d(
                        resourceId="com.yy.mobile.plugin.dreamerimx:id/btn_send")
                    if self.retry_exists(send_btn, "私聊发送按钮"):
                        self.safe_click(send_btn, "私聊发送按钮")
                        log_print("私聊消息发送成功")
                        time.sleep(self.action_short_dur)

                        # 回退收起键盘
                        d.press("back")
                        time.sleep(self.action_normal_dur)

                        # 检查是否发送成功
                        chat_rv = d(
                            resourceId="com.yy.mobile.plugin.dreamerimx:id/list")
                        sent = False
                        has_msg_status = False
                        if chat_rv.exists:
                            idx = 0
                            while True:
                                item = chat_rv.child(index=idx)
                                if not item.exists:
                                    break
                                textview = item.child(
                                    resourceId="com.yy.mobile.plugin.dreamerimx:id/tv_chatcontent")
                                if textview.exists and textview.get_text() == self.text:
                                    msg_status = item.child(
                                        resourceId="com.yy.mobile.plugin.dreamerimx:id/msg_status")
                                    if msg_status.exists:
                                        log_print("有消息消息状态红点，发送失败")
                                        has_msg_status = True
                                        break
                                    else:
                                        log_print("找到发送文案并且没有消息状态红点，发送成功")
                                        sent = True
                                    break
                                idx += 1

                        if sent:
                            log_print("私聊文案已成功发送")
                            send_result = True
                        else:
                            log_print("未检测到私聊文案发送成功")
                            if has_msg_status:
                                fail_reason = "有消息消息状态红点，发送失败"
                            else:
                                fail_reason = "未检测到私聊文案发送成功"
                    else:
                        log_print("未找到私聊发送按钮")
                        fail_reason = "未找到私聊发送按钮"
                else:
                    log_print("未找到私聊输入框")
                    fail_reason = "未找到私聊输入框"
            else:
                log_print("已经发送过相同私聊文案")
                fail_reason = "已经发送过相同私聊文案"

            # 关闭聊天窗口
            self.d.press("back")
            log_print("关闭聊天窗口")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到发消息按钮")
            fail_reason = "未找到发消息按钮"

        return send_result, fail_reason
