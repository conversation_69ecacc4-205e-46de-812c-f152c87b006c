import time
from uiautomator2 import Device, UiObject
from util.logger import log_print


def safe_ui_action(d: Device, ui_object: UiObject, action_type, log_msg, text_to_set=None) -> bool:
    """
    通用的安全UI操作方法，处理点击和设置文本，支持uiautomator2和adb备用。
    action_type: 'click' 或 'set_text'
    """
    if not ui_object.exists:
        log_print(f"[ERROR] 执行 {log_msg} 失败: 元素不存在!")
        return False

    try:
        if action_type == 'click':
            ui_object.click()
            log_print(f"[SUCCESS] 成功 {log_msg}")
        elif action_type == 'set_text':
            ui_object.set_text(text_to_set)
            log_print(f"[SUCCESS] 成功 {log_msg}: '{text_to_set}'")
        return True
    except Exception as e:
        log_print(f"[WARN] 执行 {log_msg} 失败 (uiautomator2): {e}，尝试adb备用方案。")
        bounds = ui_object.info.get("bounds")
        if bounds:
            import os
            serial = d.serial
            x = (bounds["left"] + bounds["right"]) // 2
            y = (bounds["top"] + bounds["bottom"]) // 2
            os.system(f"adb -s {serial} shell input tap {x} {y}")
            log_print(f"[INFO] 通过adb备用方案 {log_msg} 点击。")

            if action_type == 'set_text':
                # Adb tap opens keyboard, but doesn't set text. Try uiautomator2 set_text again.
                time.sleep(0.5)  # 给一点时间让键盘弹出
                if ui_object.exists:  # Re-check existence after adb tap
                    try:
                        ui_object.set_text(text_to_set)
                        log_print(
                            f"[SUCCESS] 通过adb点击后，重新设置文本成功 ({log_msg}): '{text_to_set}'")
                        return True
                    except Exception as inner_e:
                        log_print(
                            f"[ERROR] 通过adb点击后，设置文本仍然失败 ({log_msg}): {inner_e}")
                        return False
                else:
                    log_print(f"[ERROR] adb点击后，元素消失或仍无法找到 ({log_msg})!")
                    return False
            return True  # Adb click was successful for just click action
        else:
            log_print(f"[ERROR] 无法获取 {log_msg} 元素的坐标进行adb点击。")
            return False


def get_view_group_children_count(view_group: UiObject) -> int:
    """
    获取ViewGroup的子元素数量
    """
    count = 0
    while True:
        child = view_group.child(index=count)
        if not child.exists:
            break
        count += 1
    return count


def is_screen_on(d: Device) -> bool:
    screen_on = d.info.get('screenOn')
    if screen_on:
        return True
    else:
        return False
