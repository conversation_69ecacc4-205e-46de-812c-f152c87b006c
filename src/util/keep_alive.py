import time
import threading
from typing import List
import adbutils
from util.logger import log_print
import uiautomator2 as u2
from util.uiautomator_util import is_screen_on


class KeepAliveManager:
    """
    防息屏保活管理器
    在定时任务运行时持续点击屏幕，防止手机息屏或锁屏
    """

    def __init__(self, device_serials: List[str] = None):
        self.device_serials = device_serials or []
        self.keep_alive_threads = {}
        self.is_running_map = {}  # key: device_serial, value: bool

    def start_keep_alive(self, device_serial: str = None):
        """
        启动防息屏保活
        :param device_serial: 设备序列号，None表示自动选择
        """
        if device_serial in self.keep_alive_threads and self.keep_alive_threads[device_serial].is_alive():
            log_print(f"设备 {device_serial} 的防息屏保活已在运行")
            return

        thread = threading.Thread(
            target=self._keep_alive_worker, args=(device_serial,))
        # thread.daemon = True  # 设置为守护线程，主程序结束时自动结束
        thread.start()
        self.keep_alive_threads[device_serial] = thread
        self.is_running_map[device_serial] = True
        log_print(f"设备 {device_serial} 的防息屏保活已启动")

    def stop_keep_alive(self, device_serial: str = None):
        """
        停止防息屏保活
        :param device_serial: 设备序列号，None表示停止所有设备
        """
        if device_serial:
            self.is_running_map[device_serial] = False
            log_print(f"设备 {device_serial} 的防息屏保活已停止")
        else:
            for serial in self.is_running_map:
                self.is_running_map[serial] = False
            log_print("所有设备的防息屏保活已停止")

    def _keep_alive_worker(self, device_serial: str):
        """
        防息屏保活工作线程
        """
        try:
            # 获取设备
            d = adbutils.adb.device(device_serial)
            log_print(f"设备 {device_serial} 防息屏保活线程启动")

            # log_print("输入input keyevent WAKEUP唤醒")
            # d.shell("input keyevent WAKEUP")
            # time.sleep(2)
            # log_print("从左下角滑动到右上角")
            # d.swipe(0.1, 0.9, 0.9, 0.1)
            u2_device = u2.connect(device_serial)
            log_print(
                f"设备 {device_serial} 是否screenOn:{is_screen_on(u2_device)}，切到桌面")
            u2_device.press("home")
            # u2_device.unlock()

            # 获取屏幕尺寸
            width, height = d.window_size()
            log_print(f"设备 {device_serial} 屏幕尺寸: {width}x{height}")

            # 计算点击位置（屏幕中央偏下，避免影响UI操作）
            # click_x = width // 2
            # click_y = int(height * 0.8)  # 屏幕80%位置

            click_x = 1
            click_y = 1

            click_count = 0
            while self.is_running_map.get(device_serial, False):
                try:
                    # 每15秒点击一次屏幕
                    time.sleep(15)

                    if not self.is_running_map.get(device_serial, False):
                        break

                    # 点击屏幕
                    d.click(click_x, click_y)
                    click_count += 1

                    # 每20次点击记录一次日志
                    if click_count % 20 == 0:
                        log_print(
                            f"设备 {device_serial} 防息屏保活: 已点击 {click_count} 次，是否在亮屏：{is_screen_on(u2_device)}")

                except Exception as e:
                    log_print(f"设备 {device_serial} 防息屏保活点击失败: {e}")
                    time.sleep(5)  # 出错后等待5秒再重试

        except Exception as e:
            log_print(f"设备 {device_serial} 防息屏保活线程异常: {e}")

    def start_all_devices_keep_alive(self):
        """
        为所有连接的设备启动防息屏保活
        """
        try:
            # 获取所有设备
            devices = adbutils.adb.device_list()
            if not devices:
                log_print("未找到连接的设备")
                return

            for device in devices:
                self.start_keep_alive(device.serial)

        except Exception as e:
            log_print(f"启动所有设备防息屏保活失败: {e}")


# 全局防息屏管理器实例
keep_alive_manager = KeepAliveManager()


def start_keep_alive_for_scheduler():
    """
    为定时任务启动防息屏保活
    """
    log_print("定时任务启动，开始防息屏保活...")
    keep_alive_manager.start_all_devices_keep_alive()


def stop_keep_alive_for_scheduler():
    """
    停止定时任务的防息屏保活
    """
    log_print("定时任务结束，停止防息屏保活...")
    keep_alive_manager.stop_keep_alive()
