import os
from monkey_run import generate_report_text
from robot.ruliu_robot import create_meiyan_robot
from util.file_util import project_root
import json
from util.logger import log_print


def send_result_file_to_ruliu_robot(result_relative_file_name: str):
    """
    把结果文件数据发送到如流机器人
    """
    try:
        results_dir = os.path.join(project_root, 'results')
        file_name = os.path.join(results_dir, result_relative_file_name)
        with open(file_name, "r", encoding="utf-8") as f:
            result = json.load(f)
        __send_meiyan_group_robot(result)
    except Exception as e:
        log_print(f"send_result_file_to_ruliu_robot error: {e}")
        import traceback
        traceback.print_exc()


def __send_meiyan_group_robot(result):
    report_str = generate_report_text(result)
    log_print("报告文案：")
    log_print(report_str)
    # 放在子进程用不了input
    # while True:
    #     input_str = input("是否发送如流通知消息？(y/n): ")
    #     if input_str == "y":
    #         log_print("同意，发送消息")
    #         Create_Meiyan_Robot().send_message_text(report_str)
    #         break
    #     elif input_str == "n":
    #         log_print("不发送消息")
    #         break
    #     else:
    #         log_print("请输入 'y' 或 'n'！")
    # create_meiyan_robot().send_message_text(report_str)
