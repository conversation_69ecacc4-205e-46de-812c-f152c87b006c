# -*- coding: utf-8 -*-
import datetime
import threading

# 线程本地存储，用于存储当前线程的设备和应用信息
_thread_local = threading.local()


def set_log_context(device_serial: str = None, app_name: str = None):
    """设置当前线程的日志上下文"""
    _thread_local.device_serial = device_serial
    _thread_local.app_name = app_name


def get_log_context():
    """获取当前线程的日志上下文"""
    device_serial = getattr(_thread_local, 'device_serial', None)
    app_name = getattr(_thread_local, 'app_name', None)
    return device_serial, app_name


def log_print(*message):
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    device_serial, app_name = get_log_context()

    # 构建前缀
    prefix_parts = []
    if device_serial:
        prefix_parts.append(f"[{device_serial}]")
    if app_name:
        prefix_parts.append(f"[{app_name}]")

    prefix = " ".join(prefix_parts)
    message_str = " ".join(str(m) for m in message)

    if prefix:
        print(f"[{timestamp}] {prefix} {message_str}")
    else:
        print(f"[{timestamp}] {message_str}")
