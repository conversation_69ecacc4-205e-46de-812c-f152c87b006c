import logging
import threading
from pathlib import Path

# 线程本地存储，用于存储当前线程的设备和应用信息
_thread_local = threading.local()

# 全局日志文件句柄字典，用于管理不同设备/应用的日志文件
_loggers = {}


def set_log_context(device_serial=None, app_name=None, result_dir_path=None):
    if device_serial is not None:
        _thread_local.device_serial = device_serial
    if app_name is not None:
        _thread_local.app_name = app_name
    if result_dir_path is not None:
        _thread_local.result_dir_path = result_dir_path


def get_log_context():
    """获取当前线程的日志上下文"""
    return (
        getattr(_thread_local, 'device_serial', None),
        getattr(_thread_local, 'app_name', None),
        getattr(_thread_local, 'result_dir_path', None)
    )


def get_logger(device_serial=None, app_name=None, result_dir_path=None):
    """获取或创建日志logger"""
    key = f"{device_serial}_{result_dir_path}"
    if key in _loggers:
        return _loggers[key]

    logger = logging.getLogger(key)
    logger.setLevel(logging.INFO)
    logger.propagate = False  # 防止重复输出

    # 只添加一次 handler
    if not logger.handlers:
        formatter = logging.Formatter(
            '[%(asctime)s] %(message)s', datefmt='%Y-%m-%d %H:%M:%S')

        # 控制台输出
        stream_handler = logging.StreamHandler()
        stream_handler.setFormatter(formatter)
        logger.addHandler(stream_handler)

        # 文件输出（有 result_dir_path 时才加）
        if result_dir_path:
            result_dir = Path(result_dir_path)
            result_dir.mkdir(parents=True, exist_ok=True)
            filename = f"{device_serial or 'default'}.log"
            file_handler = logging.FileHandler(
                result_dir / filename, encoding='utf-8')
            print(f"设置日志文件名: {result_dir_path}/{filename}")
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)

    _loggers[key] = logger
    return logger


def log_print(*message):
    """
    打印日志，支持同时输出到控制台和文件

    Args:
        *message: 要打印的消息
    """
    device_serial, app_name, result_dir_path = get_log_context()
    logger = get_logger(device_serial, app_name, result_dir_path)
    prefix = []
    if device_serial:
        prefix.append(f"[{device_serial}]")
    if app_name:
        prefix.append(f"[{app_name}]")
    logger.info(" ".join(prefix + [str(m) for m in message]))
