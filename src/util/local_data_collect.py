import os
import json
import re
from util.file_util import project_root
from datetime import datetime, timedel<PERSON>


def collect_app_success_total(start_time, end_time):
    """
    汇总所有结果文件中，指定时间范围内所有 app 的成功次数和总次数。
    :param start_time: 开始时间（毫秒时间戳）
    :param end_time: 结束时间（毫秒时间戳）
    """
    results_dir = os.path.join(project_root, 'results')
    app_total = {}  # {app_name: {'success_count': x, 'total_count': y}}
    if not os.path.exists(results_dir):
        print(f"结果目录不存在: {results_dir}")
        return
    for run_dir in os.listdir(results_dir):
        run_dir_path = os.path.join(results_dir, run_dir)
        if not os.path.isdir(run_dir_path):
            continue
        for file in os.listdir(run_dir_path):
            if not re.match(r'result_.*\.json$', file):
                continue
            file_path = os.path.join(run_dir_path, file)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                run_start = data.get('run_start_time', 0)
                run_end = data.get('run_end_time', 0)
                if run_start >= start_time and run_end <= end_time:
                    app_result = data.get('app_result', {})
                    for app_name, result in app_result.items():
                        if app_name not in app_total:
                            app_total[app_name] = {
                                'success_count': 0, 'total_count': 0}
                        app_total[app_name]['success_count'] += result.get(
                            'success_count', 0)
                        app_total[app_name]['total_count'] += result.get(
                            'total_count', 0)
            except Exception as e:
                print(f"读取文件失败: {file_path}, 错误: {e}")
    print("统计结果：")
    for app_name, total in app_total.items():
        print(
            f"{app_name}: 成功次数: {total['success_count']}, 总共运行次数: {total['total_count']}")


def collect_app_success_total_by_date(start_date, end_date=None):
    """
    支持输入日期（如2025-7-21），统计该日期或区间内所有app的成功次数和总次数。
    :param start_date: 开始日期，格式为 'YYYY-M-D' 或 'YYYY-MM-DD'
    :param end_date: 结束日期，格式同上，若不填则只统计start_date当天
    """
    try:
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
    except ValueError:
        try:
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        except Exception:
            print("日期格式错误，应为YYYY-M-D，例如2025-7-21")
            return
    if end_date:
        try:
            end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        except Exception:
            print("结束日期格式错误，应为YYYY-M-D，例如2025-7-21")
            return
    else:
        end_dt = start_dt
    # 计算时间戳范围
    start_ts = int(start_dt.replace(hour=0, minute=0,
                   second=0, microsecond=0).timestamp() * 1000)
    end_ts = int((end_dt.replace(hour=23, minute=59, second=59,
                 microsecond=999000)).timestamp() * 1000)
    collect_app_success_total(start_ts, end_ts)
