
import adbutils
from util.logger import log_print


def screenshot(save_path: str, serial: str = None):
    """
    使用adbutils对指定设备截图并保存到本地
    :param serial: 设备序列号，默认为None（自动选择唯一设备）
    :param save_path: 截图保存路径
    """
    # 获取设备
    d = adbutils.adb.device(serial)
    # 获取截图的PIL.Image对象
    img = d.screenshot()
    # 保存到本地文件
    img.save(save_path)
    log_print(f"Screenshot saved to {save_path}")
