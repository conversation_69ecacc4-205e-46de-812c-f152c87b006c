import json
from monkey_config import AppMonkeyConfig, MonkeyConfig


def load_app_monkey_configs(config_path):
    """
    从json文件加载AppMonkeyConfig配置列表
    :param config_path: 配置文件路径
    :return: List[AppMonkeyConfig]
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    configs = []
    for item in data:
        configs.append(AppMonkeyConfig(**item))
    return configs


def load_monkey_config(config_path) -> MonkeyConfig:
    """
    从json文件加载完整的MonkeyConfig配置
    :param config_path: 配置文件路径
    :return: MonkeyConfig
    """
    with open(config_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # pydantic 会自动处理嵌套对象的转换
    return MonkeyConfig(**data)
