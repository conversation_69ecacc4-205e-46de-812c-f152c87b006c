import time
from selectors import SelectSelector


class SplashMonkey:

    def run(self, activity, d) -> bool:
        if activity == ".component.startup.main.MainActivity":
            if d(resourceId="cn.soulapp.android:id/img_soul_logo").exists and d(
                    resourceId="cn.soulapp.android:id/btn_newUser").exists:
                print("Splash page")
                time.sleep(3)
                d(resourceId="cn.soulapp.android:id/btn_newUser").click()
                print("Splash page click")
                return True
            else:
                print("Couldn't find entrance button..")
        return False