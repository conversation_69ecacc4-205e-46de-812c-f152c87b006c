import time

from config import get_config

class ImMonkey:

    def __init__(self):
        self.count = get_config()["roomCnt"]
        self.text = get_config()["text"]

    def checkFreeChat(self, d) -> bool:
        return not d(textContains="送礼开聊").exists

    def closeImWindow(self, d, index):
        print("closeImWindow index: " + str(index))
        backBtn = d(resourceId="cn.soulapp.android:id/item_left_back")
        if backBtn.exists:
            backBtn.click()
        else:
            # 第一次back是为了关闭软键盘.
            d.press("back")
            time.sleep(1.5)
            # 判断一下是否还在IM页面,如果在的话在back一次.
            if d.app_current()["activity"] == ".component.chat.ConversationActivity":
                d.press("back")

    def sendText(self, d, index) -> bool:
        print("send im message...")
        inputText = d(resourceId = "cn.soulapp.android:id/et_sendmessage")
        if inputText.exists:
            inputText.click()
            inputText.set_text(self.text)
            time.sleep(1.5)
            sendBtn = d(resourceId ="cn.soulapp.android:id/btn_send")
            if sendBtn.exists:
                #sendBtn.click()
                print("send im message success!")
                time.sleep(1.5)
                self.closeImWindow(d, index)
                return True
        return False


    def startSendText(self, d, i) -> bool:
        time.sleep(3)
        if d.app_current()["activity"] == ".component.chat.ConversationActivity":
            if self.checkFreeChat(d):
                self.sendText(d, i)
                return True
            else:
                print("Can't free chat for user index: " + str(i))
                closeBtn = d(text="先不聊了")
                if closeBtn.exists:
                    closeBtn.click()
                else:
                    self.closeImWindow(d, i)
        return False

    def openImWindow(self, d, index) -> bool:
        time.sleep(2)
        print(f"openImWindow index: {index}")
        btn = d(resourceId="cn.soulapp.android:id/tv_go_chat")
        if btn.exists:
            #点击资料卡上面的聊天.
            btn.click()
            return self.startSendText(d, index)
        else:
            print("Open im window fail!")
        return False

    def run(self, activity, d) -> bool:
        onlineList = d(resourceId="cn.soulapp.android:id/rvUser")
        if not onlineList.exists:
            print("Couldn't find online list component...")
            return False
        onlineUser = onlineList.child(resourceId="cn.soulapp.android:id/me_avatar")
        size = len(onlineUser)
        if size == 0:
            return True
        else:
            print(f"Total online count: {size}, chat count: {self.count}")
        total = 0
        # onlineUser[0].click()
        # self.openImWindow(d, 0)
        while total < self.count and total < size:
            # 打开用户资料卡
            onlineUser[total].click()
            self.openImWindow(d, total)
            total += 1
        print("finish im chat")
        return False