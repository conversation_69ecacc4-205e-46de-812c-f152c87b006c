import json
import os

import uiautomator2 as u2

from auto_soul import AutoSoul
from config import set_config


def loadConfig():
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            set_config(config)
    except FileNotFoundError:
        print("配置文件不存在")
    except json.JSONDecodeError:
        print("JSON 格式错误")

def installApk():
    current_dir = os.getcwd()
    apk = current_dir + '/apk/soul_channel_soul64.apk'
    print(apk)
    #os.system('adb install ' + apk)
    print('install success!')

def runApp():
    d = u2.connect()
    d.press("back")
    d.app_stop('cn.soulapp.android')
    d.app_start('cn.soulapp.android')

def run():
    runner = AutoSoul()
    runner.run()

if __name__ == '__main__':
    #installApk()
    #runApp()
    loadConfig()
    run()