import time
from getpass import getuser

from config import get_config


class LoginMonkey:
    def getUser(self):
        config = get_config()
        print(config)
        users = config["users"]
        for item in users:
            if "state" not in item:
                return item
        return None

    def handleLogin(self) -> bool:
        return True


    def run(self, activity, d) -> bool:
        if activity == ".component.startup.main.MainActivity":
            print("login monkey work...")
            user = self.getUser()
            if user == None:
                #print("No available accounts")
                raise ValueError("No available accounts")
            print("current user: " + str(user))
            time.sleep(3)
            tvLogin = d(resourceId="cn.soulapp.android:id/tv_login")
            if d(resourceId="cn.soulapp.android:id/pswLayout").exists and tvLogin.exists:
                print("Login activity")
                #设置密码登陆.
                if tvLogin.get_text() == "密码登录":
                    tvLogin.click()
                #隐私协议同意勾选
                d(resourceId="cn.soulapp.android:id/iv_agree").click()
                #输入账号
                d(resourceId="cn.soulapp.android:id/etPhone").set_text(user["name"])
                d(resourceId="cn.soulapp.android:id/etPwd").set_text(user["pwd"])
                d.press("back")
                time.sleep(1)
                d(resourceId="cn.soulapp.android:id/rl_login").click()
                print("Login activity click")
                time.sleep(10)
                self.handleLogin()
                return True
            else:
                print("Couldn't find password button...")
                return False
        return False
