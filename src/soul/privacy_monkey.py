import time


class PrivacyMonkey:
    def run(self, activity, d) -> bool:
        if activity == ".component.startup.main.MainActivity":
            if d(textContains="我们依据最新的监管要求更新了Soul《用户协议》和《隐私政策》").exists:
                print("Privacy dialog")
                time.sleep(3)
                if d(text="同意").exists:
                    try:
                        d(resourceId="cn.soulapp.android:id/tv_agree").click()
                        return True
                    except Exception as e:
                        print(f"点击失败: {e}")
        return False