import time
from selectors import SelectSelector

from config import get_config
from im_monkey import ImMonkey


class RoomMonkey:

    def __init__(self):
        self.text = get_config()["text"]
        self.count = get_config()["roomCnt"]
        self.monkey = ImMonkey()

    def leaveRoom(self, d):
        print("leaveRoom")
        moreBtn = d(resourceId="cn.soulapp.android:id/ivChatMore")
        if moreBtn.exists:
            moreBtn.click()
            time.sleep(1)
            exitBtn = d(text="退出派对")
            if exitBtn.exists:
                exitBtn.click()
                time.sleep(3)
                d.press("back")
                print("leaveRoom success!")
            else:
                print("leaveRoom fail exist button not found")
        else:
            print("leaveRoom fail more button not found")


    def isJoinSuccess(self, d) -> bool:
        return (d(resourceId="cn.soulapp.android:id/ivChatMore").exists
                and d(resourceId="cn.soulapp.android:id/ivChatZoomIn").exists)

    def onJoinRoomSuccess(self, d):
        print("Join room success!")
        #先发房间内的IM私聊
        self.monkey.run("", d)
        self.startSendText(d)
        for i in range(self.count):
            self.JoinNextRoom(d, i)
        time.sleep(2)
        self.leaveRoom(d)

    def JoinNextRoom(self, d, index):
        print("JoinNextRoom index:" + str(index))
        d(scrollable=True).fling()
        time.sleep(5)
        if self.isJoinSuccess(d):
            self.monkey.run("", d)
            self.startSendText(d)


    def startSendText(self, d):
        print("startSendText")
        time.sleep(5)
        chatBtn = d(resourceId="cn.soulapp.android:id/tvChat")
        if chatBtn.exists:
            chatBtn.click()
            time.sleep(1)
            self.sendText(d)
        else:
            print("Chat button not exist!")

    def sendText(self, d):
        text = self.text
        inputText = d(resourceId="cn.soulapp.android:id/etInputView")
        if inputText.exists:
            inputText.set_text(text)
            time.sleep(1)
            sendBtn = d(resourceId="cn.soulapp.android:id/btnSend")
            if sendBtn.exists:
                #发送公屏消息
                #sendBtn.click()
                print("sendText success!")
                d.press("back")

    def selectRecommend(self, d):
        viewpager = d(resourceId="cn.soulapp.android:id/tabs_chatroom_list")
        if viewpager.exists:
            recommend = viewpager.child(text="推荐")
            state = recommend.info["selected"]
            print("selectRecommend current state: " + str(state))
            if not state:
                #切换到推荐tab
                recommend.click()
                print("select recommend page")


    def run(self, d) -> bool:
        activity = d.app_current()["activity"]
        if activity == "cn.soulapp.cpnt_voiceparty.ui.ChatRoomListActivity":
            print("Room page")
            self.selectRecommend(d)
            time.sleep(2)
            #进房间按钮
            jonBtn = d(resourceId="cn.soulapp.android:id/tvJoin")
            if jonBtn.exists:
                print("start join room")
                jonBtn.click()
                time.sleep(1)
            else:
                #等待自动进入..
                time.sleep(10)
            if (self.isJoinSuccess(d)):
                self.onJoinRoomSuccess(d)
            else:
                raise TypeError("Join room fail!")
        else:
            print("Couldn't find room page..")
        return False