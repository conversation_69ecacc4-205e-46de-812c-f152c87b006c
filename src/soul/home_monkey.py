import time
from selectors import SelectSelector

from room_monkey import RoomMonkey


class HomeMonkey:

    def __init__(self):
        self.room = RoomMonkey()

    def closeYoungModeDialog(self, d):
        time.sleep(5)
        tvTitle = d(text="Soul青少年模式")
        if tvTitle.exists:
            d(resourceId="cn.soulapp.android:id/tvOk").click()
            print("Try close young mode dialog")
        else:
            print("Couldn't find young mode dialog!")

    def run(self, activity, d) -> bool:
        if activity == ".component.startup.main.MainActivity":
            self.closeYoungModeDialog(d)
            tabBar = d(resourceId="cn.soulapp.android:id/main_tab_container")
            tab1 = d(resourceId="cn.soulapp.android:id/main_tab_planet")
            if tabBar.exists and tab1.exists:
                print("Home page")
                time.sleep(3)
                tab1.click()
                time.sleep(3)
                d(scrollable=True).scroll.to(text="派对大厅")
                time.sleep(3)
                d(text="派对大厅").click()
                #等待activity打开.
                #d.wait_activity("cn.soulapp.cpnt_voiceparty.ui.ChatRoomListActivity", timeout=3)
                time.sleep(2)
                self.room.run(d)
                return True
            else:
                print("Couldn't find bottom bar...")
        return False