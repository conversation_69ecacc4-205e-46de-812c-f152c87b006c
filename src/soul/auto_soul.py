import time

import uiautomator2 as u2

from privacy_monkey import <PERSON><PERSON>vacy<PERSON>onkey
from login_monkey import <PERSON>ginMonkey
from home_monkey import Home<PERSON>onkey
from im_monkey import <PERSON>m<PERSON><PERSON>key
from splash_monkey import SplashMonkey


class AutoSoul:
    def __init__(self):
        self.d = u2.connect()
        #self.d.debug = True
        #self.monkey = [PrivacyMonkey(), SplashMonkey(), LoginMonkey(), HomeMonkey()]
        self.monkey = [HomeMonkey()]
        #self.monkey = [ImMonkey()]

    def run(self):
        info = self.d.app_current()
        if info["package"] != "cn.soulapp.android":
            return
        activity = info["activity"]
        for m in self.monkey:
            if m.run(activity, self.d):
                break