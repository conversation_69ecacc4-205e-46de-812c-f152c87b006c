import time

from BaseMonkey import BaseDialog
from util.logger import log_print
from models.terminated_monkey_error import TerminatedMonkeyError


class SoulDialog(BaseDialog):
    def __init__(self, context):
        super().__init__(context)

    # 电话权限
    def close_dialer_permission(self):
        permissionDialog = self.d(
            resourceId="com.android.permissioncontroller:id/permission_dialog"
        )
        if permissionDialog.exists:
            log_print("找到电话权限弹窗，点击确定")
            deny_view = self.d(
                resourceId="com.android.permissioncontroller:id/permission_deny_button1"
            )
            result = deny_view.click_exists()
            log_print(f"权限电话弹窗点击结果:{result}")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # 权限弹窗
    def close_permission(self):
        permissionDialog = self.d(
            resourceId="com.android.permissioncontroller:id/perm_desc_root"
        )
        if permissionDialog.exists:
            log_print("找到权限弹窗，点击确定")
            deny_view = self.d(
                resourceId="com.android.permissioncontroller:id/permission_deny_button"
            )
            result = deny_view.click_exists()
            if not result:
                deny_view = self.d(
                    resourceId="com.android.permissioncontroller:id/permission_deny_and_dont_ask_again_button"
                )
                result = deny_view.click_exists()
            log_print(f"权限弹窗点击结果:{result}")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_store_comment__dialog(self):
        """好评弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="喜欢SOUL吗？"]')
        if tmp_dialog.exists:
            log_print("好评弹窗，点击取消")
            confirm_btn = self.d.xpath('//*[@text="取消"]')
            confirm_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_permission_device_dialog(self):
        permissionDialog = self.d(
            resourceId="com.android.permissioncontroller:id/grant_singleton"
        )
        if permissionDialog.exists:
            log_print("找到权限弹窗")
            if self.d.xpath('//*[contains(@text, "设备")]').exists:
                log_print("找到设备权限弹窗，点击禁止")
                deny_view = self.d(
                    resourceId="com.android.permissioncontroller:id/permission_deny_button"
                )
                deny_view.click_exists()
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未找到设备权限弹窗")
                return False
        else:
            return False

    # dailog close
    def close_young_mode_dialog(self):
        tvTitle = self.d(text="Soul青少年模式")
        if tvTitle.exists:
            okBtn = self.d(resourceId="cn.soulapp.android:id/tvOk")
            okBtn.click_exists()
            time.sleep(self.action_normal_dur)
            print("Try close young mode dialog")
            return True
        else:
            return False

    def close_resume_join_room_dialog(self):
        # id="cn.soulapp.android:id/chatRoomCard">
        log_print("检查恢复上次聊天室对话框")
        resume_dailog = self.d(resourceId="cn.soulapp.android:id/chatRoomCard")
        if resume_dailog.exists:
            close_btn = self.d(resourceId="cn.soulapp.android:id/tv_leave")
            if close_btn.exists:
                close_btn.click_exists()
                log_print("找到上次聊天室，点击下次一定")
                time.sleep(self.action_normal_dur)
            return True
        else:
            log_print("没有找到上次聊天弹窗")
            return False

    def close_app_update__dialog(self):
        """app更新弹窗"""
        tmp_dialog = self.d.xpath('//*[@text="升级到最新版本"]')
        log_print("App 更新检查！")
        if tmp_dialog.exists:
            log_print("app更新弹窗，点击取消")
            # cn.soulapp.android:id/img_close
            close_btn = self.d(resourceId="cn.soulapp.android:id/img_close")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_account_error_dialog(self):
        account_error_dialog = self.d.xpath(
            '//*[@text="亲爱的souler，您的账号存在不友善行为，为维护Soul星氛围，您暂时无法使用此功能，感谢理解（详情可见官方通知）"]'
        )
        if account_error_dialog.exists:
            log_print("找到账号异常弹窗，终止Soul应用任务")
            raise TerminatedMonkeyError.create_terminated_app_run_account_err(
                "账号异常-账号状态异常"
            )
            # confirm_btn = self.d.xpath('//*[@text="我知道了"]')
            # confirm_btn.click_exists()
            # time.sleep(self.action_normal_dur)
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_store_comment__dialog():
            result = True
        if self.close_dialer_permission():
            result = True
        if self.close_young_mode_dialog():
            result = True
        if self.close_permission():
            result = True
        if self.close_permission_device_dialog():
            result = True
        if self.close_account_error_dialog():
            result = True
        return result
