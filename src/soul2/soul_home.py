import time
from BaseMonkey import ActivityMonkey, DEFAULT_TERMINATE_ON_HIT_FAIL_COUNT
from soul2.soul_dialog import SoulDialog
from soul2.soul_room import SoulRoomMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import UiObject

# Soul首页脚本
class SoulHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config, room_monkey: ActivityMonkey):
        # 首页Activity
        # cn.soulapp.android/.component.startup.main.MainActivity t49398}
        super().__init__(context, app_config, ".component.startup.main.MainActivity")
        
        self.room_monkey = room_monkey
        self.dialog_monkey = SoulDialog(context)
        self.live_room_list = []
        self.fail = 0
        self.retry_action = self.check_when_retry
        self.__fail_to_go_room_count = 0


    def check_when_retry(self, tag: str = None, i: int = 0):
        log_print(f"home check_when_retry {tag} {i}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)
        time.sleep(self.action_short_dur)

    def run(self):
        try:
            self.dialog_monkey.close_app_update__dialog()
            self.dialog_monkey.try_dismiss()
            time.sleep(self.action_short_dur)
            self.run_home()
        except TerminatedMonkeyError as e:
            if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                log_print("收到完成信号，Soul任务已完成")
            else:
                raise e
        
        return True


    def run_home(self):
        d = self.d
        # 1. 切换到底部星球tab
        # tabBar = d(resourceId="cn.soulapp.android:id/main_tab_container")
        # tab1 = d(resourceId="cn.soulapp.android:id/main_tab_planet")
        planet_tab = d(resourceId="cn.soulapp.android:id/main_tab_planet")
        log_print(f"开始运行{self.app_config.name} home")
        if self.retry_exists(planet_tab, "planet_tab"):
            log_print("点击底部星球 tab")
            self.safe_click(planet_tab, "planet_tab")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到星球 tab，退出")
            return
        log_print(f"点击底部星球 tab 成功")
        d(scrollable=True).scroll.to(text="派对大厅")
        time.sleep(self.action_short_dur)


        self.process_room_list()

        if self._fail_reason_dict:
            log_print("\n失败原因统计：")
            for reason, count in self._fail_reason_dict.items():
                log_print(f"{reason}: {count} 次")
        else:
            log_print("无失败原因，全部成功！") 

        return True


    def process_room_list(self):
        self.__fail_to_go_room_count = 0
        while True:
            if self._process >= self.app_config.count:
                log_print("已完成进入次数..")
                break

            self.dialog_monkey.try_dismiss()
            item_containers = self.d(resourceId="cn.soulapp.android:id/tvTitle") #cn.soulapp.android:id/recyclerView
            if not self.retry_exists(item_containers, "item_containers", 3):
                log_print("未找到聊天室列表 process_room_list item失败，尝试滑动")
                self.switch_live_room(self.d)
                continue

            item_count = item_containers.count
            if item_count == 0:
                log_print("未找到聊天室列表 item_containers空，尝试滑动")
                self.switch_live_room(self.d)
                continue
            
            log_print(f"找到{item_count}个聊天室")

            for i in range(item_count):
                if self._process >= self.app_config.count:
                    log_print("已完成进入次数..")
                    break
                
                item = item_containers[i]
                
                # try:
                self.find_room(item, i)
                # except Exception as e:
                #     log_print(f"处理第 {i+1} 个聊天室时出错: {str(e)}")
                #     if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                #         log_print("【in】收到完成信号，Soul任务已完成")
                #         raise e
                #     self.check_activity_and_back()
                #     continue


            self.switch_live_room(self.d)
                    

    def find_room(self, item: UiObject, i: int) -> bool:
        # cn.soulapp.android:id/tvTitle
        topic_name_view = item
        if self.retry_exists(topic_name_view, "topic_name_view"):
            topic_name = topic_name_view.get_text() #获取名字
            log_print(f"找到第{i+1}个聊天室，主题：{topic_name}")
            # 判断topic_name是否在live_room_list中
            if topic_name in self.live_room_list:
                log_print(f"第{i+1}个聊天室主题：{topic_name} 已存在，跳过")
                return False
            self.live_room_list.append(topic_name) #todoOpt
            self.safe_click(topic_name_view, "topic_name_view")
            log_print(f"点击聊天室：{topic_name}")
            time.sleep(self.action_normal_dur)
            self.dialog_monkey.close_resume_join_room_dialog() #fizzc
            self.dialog_monkey.try_dismiss()
            room_monkey = SoulRoomMonkey(self.context, self.app_config)
            time.sleep(self.action_normal_dur)
            def progress_callback(progress: int, total_process: int):
                log_print(f"进度: {progress}/{total_process}")
                if progress <= 1:
                    return
                result_list = room_monkey.send_privately_result_list
                fail_reason_list = room_monkey.send_privately_fail_reason_list
                real_index = progress - 1 - 1
                send_result = result_list[real_index]
                fail_reason = fail_reason_list[real_index]
                self.do_process(self._process + 1)
                if send_result:
                    self._success_count += 1
                else:
                    self.fail += 1
                    if fail_reason:
                        if fail_reason in self._fail_reason_dict:
                            self._fail_reason_dict[fail_reason] += 1
                        else:
                            self._fail_reason_dict[fail_reason] = 1
                log_print(f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {self.fail}, 失败原因: {fail_reason}")
                if self._process >= self.app_config.count:
                    log_print("[In]已完成进入次数..")
                    raise TerminatedMonkeyError.create_process_finished()

            log_print(self.d.app_current()["activity"])
            
            if self.d.app_current()["activity"] == room_monkey.activity:
                room_monkey.register_progress_callback(progress_callback)      
                room_monkey.run()      
                room_monkey.unregister_progress_callback(progress_callback)
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未能正常进入直播间，再次尝试关闭弹窗")
                time.sleep(self.action_long_dur)
                self.dialog_monkey.try_dismiss()
                time.sleep(self.action_normal_dur)
                if self.d.app_current()["activity"] == room_monkey.activity:
                    self.__fail_to_go_room_count = 0
                    room_monkey.register_progress_callback(progress_callback)      
                    room_monkey.run()      
                    room_monkey.unregister_progress_callback(progress_callback)
                    time.sleep(self.action_normal_dur)
                    return True
                else:
                    self.__fail_to_go_room_count += 1
                    log_print(f"未能正常进入直播间，失败。连续次数：{self.__fail_to_go_room_count}")
                    if self.__fail_to_go_room_count >= DEFAULT_TERMINATE_ON_HIT_FAIL_COUNT:
                        # 因为功能受限时以toast形势交互，元素找不到toast信息，所以通过次数判断
                        log_print(f"连续未能进入直播间次数达{self.__fail_to_go_room_count}次，可能是账号异常，功能受限，按照账号异常处理")
                        raise TerminatedMonkeyError.create_terminated_app_run_account_err("账号异常-进入直播间功能受限")
                    return False



    def has_more_tofu(self):
        """检查是否还有更多豆腐块可点击"""
        rv = self.d(resourceId="cn.soulapp.android:id/tvTitle")
        return rv.exists

    def switch_live_room(self, d):
        """向上滑动切换直播间列表"""
        if self.d.app_current()["activity"] !=  self.activity:
            log_print("【swipe】 activity 不是 home return")
            return

        width, height = d.window_size()
        
        for attempt in range(3):
            x = width * (0.5 + 0.01 * attempt)
            y_start = height * (0.8 - 0.02 * attempt)
            y_end = height * (0.3 + 0.02 * attempt)
            log_print(f"向上滑动切换直播间列表 attempt:{attempt+1} from:{x},{y_start} to:{x},{y_end}")
            d.swipe(x, y_start, x, y_end, 0.1)
            time.sleep(self.action_normal_dur)
            
            # 检查是否有新的豆腐块
            if self.has_more_tofu():
                log_print("滑动后找到新的豆腐块")
                break
        else:
            log_print("多次滑动后仍未找到新的豆腐块")