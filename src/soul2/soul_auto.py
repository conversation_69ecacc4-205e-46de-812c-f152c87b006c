import time
from BaseMonkey import A<PERSON>Monkey
from monkey_config import AppMonkeyConfig
from soul2.soul_home import <PERSON>H<PERSON>Monkey
from soul2.soul_room import Soul<PERSON><PERSON><PERSON>onkey

from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext


# Soul App 自动灌水脚本
class SoulAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = SoulRoomMonkey(context, app_config)
        self.home_monkey = SoulHomeMonkey(context, app_config, self.room_monkey)
        self.proxy_monkey = self.home_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self) -> str:
        return "cn.soulapp.android"

    def run(self):
        # 执行首页逻辑
        self.home_monkey.run()
        
        log_print("Soul 自动任务完成！")