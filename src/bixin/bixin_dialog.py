import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


# 比心弹窗处理
class BixinDialog(BaseDialog):

    def close_young_mode_dialog(self):
        young_mode_dialog = self.d.xpath('//*[@text="比心青少年模式"]')
        if young_mode_dialog.exists:
            log_print("找到了青少年模式弹窗，关闭")
            know_btn = self.d.xpath('//*[@text="我知道了"]')
            know_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_channel_forbidden_dialog(self):
        """预留，禁止进入直播间弹窗，还没用"""
        return False

    def close_activity_dialog(self):
        activity_dialog = self.d(
            resourceId="com.yitantech.gaigai:id/ivActivityImg")
        if activity_dialog.exists:
            log_print("找到了活动弹窗，关闭")
            close_btn = self.d(resourceId="com.yitantech.gaigai:id/ivClose")
            close_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_floating_window_permission_dialog(self, just_close: bool):
        """这个是在直播间按返回时弹出，但直播间有一个web id，误判了点击back，会触发这个弹窗，所以做一个区分"""
        dialog = self.d.xpath('//*[@text="用于聊天室小窗、直播间小窗播放等操作，请前往开启权限"]')
        if dialog.exists:
            if just_close:
                log_print("找到悬浮窗权限弹窗，点击返回关闭弹窗")
                self.d.press("back")
            else:
                log_print("找到悬浮窗权限弹窗，点击暂不开启")
                not_open_btn = self.d.xpath('//*[@text="暂不开启"]')
                not_open_btn.click_exists()
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_web_dialog(self):
        """不准，直播间有一个这个id，但没找到web，不是弹窗"""
        dialog = self.d(resourceId="com.yitantech.gaigai:id/h5_state_view")
        if dialog.exists:
            log_print("找到web弹窗，点击返回")
            self.d.press("back")
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def close_new_user_sign_dialog(self):
        dialog = self.d.xpath('//*[@text="签到领取"]')
        if dialog.exists:
            log_print("找到新用户签到领取弹窗，点击返回")
            self.d.press("back")
            time.sleep(self.action_short_dur)
            return True
        else:
            return False

    def process_login(self):
        activity = self.d.app_current()["activity"]
        if activity == "com.bx.login.login.LoginHomeActivity":
            log_print("找到登录页面，终止程序")
            raise TerminatedMonkeyError.create_terminated_app_run_account_err("账号异常-未登录")
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_channel_forbidden_dialog():
            result = True
        if self.close_young_mode_dialog():
            result = True
        if self.close_activity_dialog():
            result = True
        if self.close_floating_window_permission_dialog(True):
            result = True
        # if self.close_web_dialog():
        #     result = True
        if self.close_new_user_sign_dialog():
            result = True
        if self.process_login():
            result = True
        return result
