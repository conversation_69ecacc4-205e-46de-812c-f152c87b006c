import time
from typing import <PERSON><PERSON>

from uiautomator2 import UiObject

from BaseMonkey import ActivityMonkey
from bixin.bixin_dialog import BixinDialog
from bixin.bixin_room import BixinRoomMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print


# 比心首页脚本
class BixinHomeMonkey(ActivityMonkey):
    def __init__(self, context, app_config):
        # 首页Activity
        super().__init__(context, app_config, "com.bx.container.main.MainActivity")
        self.dialog_monkey = BixinDialog(context)
        self.live_room_list = []
        self.rv_index = 0
        self.fail = 0
        self.retry_action = self.check_when_retry
        self.switch_and_can_not_find_count = 0

    def check_when_retry(self, tag: str, retry_count: int = 0):
        log_print(f"home check_when_retry {tag} {retry_count}")
        self.dialog_monkey.close_web_dialog()
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def check_activity_and_back(self, retry_count: int = 5, expand_fail: bool = True):

        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            elif activity == "com.ypp.chatroom_container.ui.room.ChatRoomActivity":
                self.d.press("back")
                time.sleep(self.action_short_dur)
                close_floating_window = self.dialog_monkey.close_floating_window_permission_dialog(False)
                if close_floating_window:
                    time.sleep(self.action_normal_dur)
                return False, "当前为直播间Activity，退出直播间"
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_long_dur}秒"
                log_print(f"{error_msg}")
                time.sleep(self.action_long_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back,
                              None, None, retry_count, expand_fail)

    def run(self):
        try:
            self.run_home()
        except TerminatedMonkeyError as e:
            if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                log_print("收到完成信号，比心任务完成")
            else:
                raise e

        return True

    def run_home(self):
        # 1. 切换到底部第二个tab
        entertainment_tab = self.d.xpath('(//*[@resource-id="com.yitantech.gaigai:id/bottomItemContainer"])[3]')

        if self.retry_exists(entertainment_tab, "查找底部第二个tab"):
            log_print("找到底部第二个tab，点击")
            self.safe_click(entertainment_tab, "点击底部第二个tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到底部第二个tab")

        # 2. 切换到“聊天室”tab
        chat_tab = self.d.xpath('//*[@text="聊天室"]')
        if self.retry_exists(chat_tab, "查找聊天室tab"):
            log_print("找到聊天室tab，点击")
            self.safe_click(chat_tab, "点击聊天室tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到聊天室tab")

        # 3. 切换到“推荐”子tab
        hot_tab = self.d.xpath('//*[@text="推荐"]')
        if self.retry_exists(hot_tab, "查找推荐tab"):
            log_print("找到推荐tab，点击")
            self.safe_click(hot_tab, "点击推荐tab")
            time.sleep(self.action_short_dur)
        else:
            raise TerminatedMonkeyError.create_terminated_app_run("未找到推荐tab")

        self.process_room_list()

        return True

    def process_room_list(self):

        self.rv_index = 0

        while True:
            if self._process >= self._total_process:
                log_print("已完成进入次数..")
                break

            self.dialog_monkey.try_dismiss_all()

            rv = self.d(resourceId="com.yitantech.gaigai:id/rvRecommendN")

            if not self.retry_exists(rv, "查找推荐列表"):
                log_print("未找到推荐列表 process_room_list 失败")
                break

            item = rv.child(index=self.rv_index)
            if not self.retry_exists(item, f"查找item: {self.rv_index}", 1):
                log_print(f"未找到item: {self.rv_index}，尝试滑动")
                self.switch_live_room(self.d)
                self.rv_index = 0
                continue

            if not self.check_room_item(item, self.rv_index):
                self.rv_index += 1
                continue

            find_room_result = self.find_room(item, self.rv_index)
            if not find_room_result:
                if self.switch_and_can_not_find_count > 5:
                    log_print(f"多次尝试滑动，仍未找到符合条件的item，可能已经滑到底，任务完成")
                    raise TerminatedMonkeyError.create_process_finished()
                log_print(f"未找到符合find_room条件的item: {self.rv_index}，尝试滑动")
                self.switch_live_room(self.d)
                self.rv_index = 0
                self.switch_and_can_not_find_count += 1
                continue

            log_print("查找下一个条目")
            self.switch_and_can_not_find_count = 0
            self.rv_index += 1

    def check_room_item(self, item: UiObject, i: int) -> bool:
        """检查是否是聊天室类型的item"""
        topic_name_view = item.child(resourceId="com.yitantech.gaigai:id/tvCRHTitle")
        if topic_name_view.exists:
            return True
        log_print(f"检查到索引{i}不是普通聊天室类型条目")
        return False

    def find_room(self, item: UiObject, i: int) -> bool:
        topic_name_view = item.child(resourceId="com.yitantech.gaigai:id/tvCRHTitle")
        if topic_name_view.exists:
            topic_name = topic_name_view.get_text()
            log_print(f"找到第{i+1}个聊天室，主题：{topic_name}")
            # 判断topic_name是否在live_room_list中
            if topic_name in self.live_room_list:
                log_print(f"第{i+1}个聊天室主题：{topic_name} 已存在，跳过")
                return False
            self.live_room_list.append(topic_name)
            self.safe_click(topic_name_view, f"topic_name_view: {i}")
            log_print(f"点击聊天室：{topic_name}")
            time.sleep(self.action_normal_dur)
            if self.dialog_monkey.close_channel_forbidden_dialog():
                log_print(f"第{i+1}个聊天室主题：{topic_name} 禁止进入，跳过")
                return False
            self.dialog_monkey.try_dismiss_all()
            room_monkey = BixinRoomMonkey(self.context, self.app_config)
            time.sleep(self.action_long_dur)

            def progress_callback(progress: int, total_process: int):
                log_print(f"进度: {progress}/{total_process}")
                if progress <= 1:
                    return
                result_list = room_monkey.send_privately_result_list
                fail_reason_list = room_monkey.send_privately_fail_reason_list
                real_index = progress - 1 - 1
                send_result = result_list[real_index]
                fail_reason = fail_reason_list[real_index]
                if fail_reason == "ignore":
                    return

                if send_result:
                    self._success_count += 1
                else:
                    self.fail += 1
                    if fail_reason:
                        if fail_reason in self._fail_reason_dict:
                            self._fail_reason_dict[fail_reason] += 1
                        else:
                            self._fail_reason_dict[fail_reason] = 1
                log_print(
                    f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {self.fail}, 失败原因: {fail_reason}"
                )
                self.do_process(self._process + 1)
                if self._process >= self._total_process or self._success_count >= 30:
                    log_print("已完成进入次数，发送终止信号..")
                    raise TerminatedMonkeyError.create_process_finished()

            room_monkey.register_progress_callback(progress_callback)
            room_monkey.run()
            room_monkey.unregister_progress_callback(progress_callback)
            time.sleep(self.action_normal_dur)
            return True

        return False

    def has_more_tofu(self):
        """检查是否还有更多豆腐块可点击"""
        rv = self.d(resourceId="com.yitantech.gaigai:id/rvRecommendN")
        if rv.exists:
            topic_name_view = rv.child(resourceId="com.yitantech.gaigai:id/tvCRHTitle")
            if topic_name_view.exists:
                topic_name = topic_name_view.get_text()
                if topic_name in self.live_room_list:
                    return False
                else:
                    return True
        return False

    def switch_live_room(self, d):
        """向上滑动切换直播间列表"""
        width, height = d.window_size()
        for attempt in range(3):
            x = width * (0.5 + 0.01 * attempt)
            y_start = height * (0.8 - 0.02 * attempt)
            y_end = height * (0.3 + 0.02 * attempt)
            log_print(
                f"向上滑动切换直播间列表 attempt:{attempt+1} from:{x},{y_start} to:{x},{y_end}"
            )
            d.swipe(x, y_start, x, y_end, 0.1)
            time.sleep(self.action_normal_dur)

            # 检查是否有新的豆腐块
            if self.has_more_tofu():
                log_print("滑动后找到新的豆腐块")
                break
        else:
            log_print("多次滑动后仍未找到新的豆腐块")
