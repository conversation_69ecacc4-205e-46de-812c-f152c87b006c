import time

from BaseMonkey import <PERSON><PERSON><PERSON>onkey
from bixin.bixin_home import B<PERSON><PERSON><PERSON><PERSON>Monkey
from models.monkey_context import <PERSON><PERSON>ontext
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_proxy_mixin import MonkeyProxyMixin
from monkey_config import AppMonkeyConfig
from util.logger import log_print

# 比心脚本


class BixinAuto(MonkeyProxyMixin, AppMonkey):

    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.home_monkey = BixinHomeMonkey(context, app_config)
        self.proxy_monkey = self.home_monkey

    def get_app_package_name(self) -> str:
        return "com.yitantech.gaigai"

    def run(self) -> bool:
        log_print("开始运行比心脚本")
        loop_count = 0
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            log_print(f"检测当前activity:{activity}")
            if activity == self.home_monkey.activity:
                log_print("运行首页脚本")
                self.home_monkey.run()
                return True

            if activity == "com.bx.login.login.LoginHomeActivity":
                raise TerminatedMonkeyError.create_terminated_app_run_account_err("账号异常-未登录")

            if loop_count > 10:
                raise TerminatedMonkeyError.create_terminated_app_run(
                    "启动应用后多次等待仍检测不到首页Activity")
            log_print("等待首页activity..")
            time.sleep(self.action_normal_dur)
            loop_count += 1
