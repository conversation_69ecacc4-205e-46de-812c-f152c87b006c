import random
import time
from typing import List, Tuple
from BaseMonkey import ActivityMonkey
from bixin.bixin_dialog import BixinDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from uiautomator2 import Device

IS_TEST = False


# 比心直播间脚本
class BixinRoomMonkey(ActivityMonkey):
    def __init__(self, context, app_config):
        super().__init__(context, app_config,
                         "com.ypp.chatroom_container.ui.room.ChatRoomActivity")
        self.text = app_config.text
        self.text_publicly = app_config.text_publicly
        self.dialog_monkey = BixinDialog(context)
        self.send_publicly_result = False
        self.send_publicly_fail_reason = ""
        self.send_privately_result_list: List[bool] = [False]
        self.send_privately_fail_reason_list: List[str] = [""]
        self.user_nick_list: List[str] = []
        self.retry_action = self.check_when_retry

    def check_when_retry(self, tag: str, retry_count: int = 0):
        log_print(f"room check_when_retry {tag} {retry_count}")
        self.dialog_monkey.try_dismiss()
        time.sleep(self.action_normal_dur)
        self.check_activity_and_back(expand_fail=False)

    def check_activity_and_back(self, retry_count: int = 3, expand_fail: bool = True):
        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            elif activity == "com.bx.container.main.MainActivity":
                log_print(f"当前activity: {activity}，是home，终止任务")
                raise TerminatedMonkeyError(101, "页面是首页，终止直播间任务")
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_normal_dur}秒"
                log_print(error_msg)
                time.sleep(self.action_normal_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back,
                              None, None, retry_count, expand_fail)

    def on_hit_fail_action(self, fail_reason: str, count: int):
        if count > 5:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == "com.bx.container.main.MainActivity" or activity == self.activity:
                log_print(f"多次连续命中失败原因{fail_reason}，终止直播间任务")
                raise TerminatedMonkeyError(101, "多次连续命中失败原因，终止直播间任务")
            else:
                log_print(f"多次连续命非法activity，重新start")
                self.d.app_start("com.yitantech.gaigai")
        super().on_hit_fail_action(fail_reason, count)

    def run(self):
        try:
            self.run_room()
        except TerminatedMonkeyError as e:
            if e.code == 101:
                log_print(f"页面是首页，终止直播间任务")
                return False
            else:
                raise e
        return True

    def run_room(self):
        if IS_TEST:
            time.sleep(4)
            print("IS_TEST直播间完成")
            self.send_publicly_result = True
            self.send_privately_result_list = [True] * 1
            self.send_privately_fail_reason_list = [""] * 1
            self._total_process = 2
            self.do_process(2)
            self.close_room()
            return True

        d = self.d
        time.sleep(self.action_normal_dur)

        # 1. 公屏发送消息
        send_publicly_result, send_publicly_fail_reason = self.chat_publicly()
        self.send_publicly_result = send_publicly_result
        self.send_publicly_fail_reason = send_publicly_fail_reason
        self.do_process(1)

        # 2. 私聊所有用户头像
        self.chat_all_users_privately(d)

        log_print("直播间操作完成，退出直播间")
        self.close_room()
        return None

    def close_room(self):
        self.d.press("back")
        time.sleep(self.action_short_dur)
        close_floating_window = self.dialog_monkey.close_floating_window_permission_dialog(
            False)
        if close_floating_window:
            time.sleep(self.action_normal_dur)

    def chat_publicly(self):
        """公屏发送消息"""
        send_result = False
        fail_reason = ""

        if not self.text_publicly:
            return True, ""

        # 点击公屏发送入口
        chat_entry = self.d(
            resourceId="com.yitantech.gaigai:id/tvChat")
        if self.retry_exists(chat_entry, "查找公屏发送入口"):
            log_print("找到公屏发送入口，点击")
            self.safe_click(chat_entry, "点击公屏发送入口")
            time.sleep(self.action_short_dur)

            # 输入内容
            input_box = self.d(
                resourceId="com.yitantech.gaigai:id/etInput")
            if self.retry_exists(input_box, "查找公屏输入框"):
                log_print("找到公屏输入框，设置公屏语句")
                self.safe_set_text(input_box, self.text_publicly, "在公屏输入框设置语句")
                time.sleep(self.action_short_dur)

                # 点击发送
                send_btn = self.d(
                    resourceId="com.yitantech.gaigai:id/tvSend")
                if self.retry_exists(send_btn, "查找公屏发送按钮"):
                    log_print("找到公屏发送按钮，点击")
                    self.safe_click(send_btn, "公屏发送按钮")
                    time.sleep(self.action_short_dur)
                    # self.d.press("back")
                    send_result = True
                    log_print("公屏消息发送成功")
                    time.sleep(self.action_short_dur)
                else:
                    log_print("未找到公屏发送按钮")
                    fail_reason = "未找到公屏发送按钮"
            else:
                log_print("未找到公屏输入框")
                fail_reason = "未找到公屏输入框"
        else:
            log_print("未找到公屏发送入口")
            fail_reason = "未找到公屏发送入口"

        return send_result, fail_reason

    def chat_all_users_privately(self, d: Device):
        """私聊所有用户头像"""
        # 查找所有用户头像
        avatar_containers = d(
            resourceId="com.yitantech.gaigai:id/layoutSeatRoot")

        if not self.retry_exists(avatar_containers, "查找用户头像容器"):
            log_print("未找到用户头像容器")
            return

        # 获取所有头像的数量
        avatar_count = avatar_containers.count
        log_print(f"找到 {avatar_count} 个用户头像")
        self._total_process = avatar_count + 1  # +1是公屏
        self.send_privately_result_list = [False] * avatar_count
        self.send_privately_fail_reason_list = ["ignore"] * avatar_count

        # 打印所有昵称名字
        name_list = []
        for i in range(avatar_count):
            avatar_container = avatar_containers[i]
            user_nick_view_in_avatar = avatar_container.child(
                resourceId="com.yitantech.gaigai:id/txvName")
            if user_nick_view_in_avatar.exists:
                name_list.append(user_nick_view_in_avatar.get_text())
        log_print(f"所有用户名字：{name_list}")

        # 遍历所有头像
        for i in range(avatar_count):
            try:
                # 获取第i个头像
                avatar = avatar_containers[i]
                if self.retry_exists(avatar, f"查找用户头像: {i + 1}"):
                    user_nick_view_in_avatar = avatar.child(
                        resourceId="com.yitantech.gaigai:id/txvName")
                    user_nick_name = "未知"
                    if user_nick_view_in_avatar.exists:
                        user_nick_name = user_nick_view_in_avatar.get_text()

                    if user_nick_name == "等待上麦":
                        log_print(f"第{i + 1}个是未上麦坑位，忽略")
                        continue

                    if user_nick_name in self.user_nick_list:
                        log_print(f"第{i + 1}个坑位，昵称：{user_nick_name}已经处理过，忽略")
                        continue

                    self.user_nick_list.append(user_nick_name)
                    log_print(f"点击第 {i + 1} 个用户头像, {user_nick_name}")
                    self.safe_click(avatar, f"点击用户头像: {i + 1}")
                    time.sleep(self.action_short_dur)

                    # 检查符合弹窗的元素
                    is_card_dialog = self.check_click_user_card_dialog()
                    if not is_card_dialog:
                        log_print(f"检查到第{i + 1}点击后不符合用户卡片，移除弹窗并继续遍历")
                        self.d.press("back")
                        time.sleep(self.action_short_dur)
                        continue

                    self.follow(user_nick_name)

                    self.process_in_user_card(user_nick_name, i)

                    log_print("处理完卡片私聊任务，点击返回关闭")
                    self.d.press("back")
                    time.sleep(self.action_short_dur)

                    # 减慢速度
                    time.sleep(40)

                else:
                    log_print(f"第 {i + 1} 个头像不存在")
                    break
            except Exception as e:
                if isinstance(e, TerminatedMonkeyError):
                    raise e
                else:
                    log_print(f"处理第 {i + 1} 个头像时出错: {str(e)}")
                    continue

    def check_click_user_card_dialog(self):
        """检测是否符合用户卡片弹窗"""
        message_btn = self.d.xpath('//*[@text="聊天"]')
        if message_btn.exists:
            return True
        return False

    def process_in_user_card(self, nick_name: str, i: int):
        """"用户卡片操作"""
        self.follow(nick_name)
        # 执行私聊步骤
        send_privately_result, send_privately_fail_reason = self.chat_single_user_privately()
        self.send_privately_result_list[i] = send_privately_result
        self.send_privately_fail_reason_list[i] = send_privately_fail_reason

        self.do_process(i + 1 + 1)  # + 1是当前头像进度，再+1是公屏

    def follow(self, nick_name: str):
        follow_view = self.d.xpath('//*[@text="关注"]')
        un_follow_view = self.d.xpath('//*[@text="已关注"]')
        if follow_view.exists:
            follow_status = follow_view.get_text()
            log_print(f"关注按钮的文案：{follow_status}")
            log_print(f"找到关注按钮，点击关注 {nick_name}")
            self.safe_click(follow_view)
            time.sleep(self.action_short_dur)
        elif un_follow_view.exists:
            log_print(f"已关注 {nick_name}")
        else:
            log_print(f"找不到关注按钮，无法关注 {nick_name}")

    def chat_single_user_privately(self):
        """对单个用户执行私聊步骤"""
        send_result = False
        fail_reason = ""
        # 点击发消息按钮
        message_btn = self.d.xpath('//*[@text="聊天"]')
        if self.retry_exists(message_btn, "查找聊天按钮"):
            log_print("找到聊天按钮，点击")
            self.safe_click(message_btn, "点击聊天按钮")
            time.sleep(self.action_normal_dur)

            # 检查是否已发送过相同文案
            chat_rv = self.d(resourceId="com.yitantech.gaigai:id/rvMsgList")
            has_sent = False
            if self.retry_exists(chat_rv, "查找聊天列表"):
                idx = 0
                while True:
                    item = chat_rv.child(index=idx)
                    if not item.exists:
                        break
                    textview = item.child(
                        resourceId="com.yitantech.gaigai:id/txvMsgContent")
                    if textview.exists and textview.get_text() == self.text:
                        has_sent = True
                        log_print("已发送过相同私聊文案")
                        break
                    idx += 1

            if not has_sent:
                # 输入私聊文案
                input_box = self.d(
                    resourceId="com.yitantech.gaigai:id/etInputContent")

                if self.retry_exists(input_box, "查找私聊输入框"):
                    r = random.random()
                    if r < 0.5:
                        text = self.text
                    elif r < 0.8:
                        text = "哈喽~"
                    else:
                        text = "你好~"

                    self.safe_set_text(input_box, text, "在私聊输入框设置语句")
                    time.sleep(self.action_short_dur)
                    send_btn = self.d(
                        resourceId="com.yitantech.gaigai:id/flSendMessage")
                    if self.retry_exists(send_btn, "查找私聊发送按钮"):
                        self.safe_click(send_btn, "点击私聊发送按钮")
                        log_print("私聊消息发送成功")
                        time.sleep(self.action_short_dur)

                        # 回退收起键盘
                        self.d.press("back")
                        time.sleep(self.action_normal_dur)

                        # 检查是否发送成功
                        sent = False
                        if chat_rv.exists:
                            idx = 0
                            while True:
                                item = chat_rv.child(index=idx)
                                if not item.exists:
                                    break
                                textview = item.child(
                                    resourceId="com.yitantech.gaigai:id/txvMsgContent")
                                if textview.exists and textview.get_text() == self.text:
                                    log_print("找到发送文案并且没有消息状态红点，发送成功")
                                    sent = True
                                    break
                                idx += 1

                        if sent:
                            log_print("私聊文案已成功发送")
                            send_result = True
                        else:
                            log_print("未检测到私聊文案发送成功")
                            fail_reason = "未检测到私聊文案发送成功"
                    else:
                        log_print("未找到私聊发送按钮")
                        fail_reason = "未找到私聊发送按钮"
                else:
                    log_print("未找到私聊输入框")
                    fail_reason = "未找到私聊输入框"
            else:
                log_print("已经发送过相同私聊文案")
                fail_reason = "已经发送过相同私聊文案"

            # 关闭聊天窗口
            self.d.press("back")
            log_print("关闭聊天窗口")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到发消息按钮")
            fail_reason = "未找到发消息按钮"

        return send_result, fail_reason
