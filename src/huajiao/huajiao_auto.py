import time
from BaseMonkey import AppMonkey
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from huajiao.home_monkey import HomeMonkey
from huajiao.room_monkey import RoomMonkey
from monkey_proxy_mixin import MonkeyProxyMixin
from util.logger import log_print
from models.monkey_context import MonkeyContext

# 花椒直播私聊主播自动脚本


class HuajiaoAuto(MonkeyProxyMixin, AppMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config)
        self.room_monkey = RoomMonkey(context, app_config)
        self.home_monkey = HomeMonkey(context, app_config, self.room_monkey)
        self.proxy_monkey = self.room_monkey
        self.proxy_hit_fail_action_monkeys.append(self.home_monkey)
        self.proxy_hit_fail_action_monkeys.append(self.room_monkey)

    def get_app_package_name(self):
        return "com.huajiao"

    def run(self):
        loop_count = 0
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            else:
                error_msg = f"等待花椒对应的Activity，当前Activity: {activity}"
                log_print(f"{error_msg}")
                time.sleep(2)
                if loop_count > 15:
                    raise TerminatedMonkeyError.create_terminated_app_run(
                        error_msg)
                loop_count += 1
