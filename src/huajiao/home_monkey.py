import time
from BaseMonkey import ActivityMonkey
from huajiao.huajiao_dialog import HuajiaoDialog
from huajiao.room_monkey import RoomMonkey
from monkey_config import AppMonkeyConfig
from util.logger import log_print
from models.monkey_context import MonkeyContext


class HomeMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig, room_monkey: RoomMonkey):
        # "com.huajiao/.main.MainActivity"
        super().__init__(context, app_config, ".main.MainActivity")
        self.room = room_monkey
        self.dialog = HuajiaoDialog(context)

    def run(self):
        d = self.d
        time.sleep(self.action_short_dur)
        self.dialog.try_dismiss()
        # 切换到热门tab
        tab_bar = d(resourceId="com.huajiao:id/pager_sliding_tab")
        hot_tab = tab_bar.child(text="热门")
        if self.retry_exists(hot_tab, "热门tab"):
            log_print("找到热门tab，点击热门tab")
            self.safe_click(hot_tab, "热门tab")
            time.sleep(self.action_normal_dur)

        self.dialog.try_dismiss()

        # 进入第一个直播
        rv = d(resourceId="com.huajiao:id/id_stickynavlayout_innerscrollview")
        first_item = rv.child(index=0)
        if self.retry_exists(first_item, "第一个直播豆腐块"):
            log_print("找到第一个直播豆腐块，点击第一个直播豆腐块")
            self.safe_click(first_item, "第一个直播豆腐块")
            time.sleep(self.action_normal_dur)
            self.room.run()
