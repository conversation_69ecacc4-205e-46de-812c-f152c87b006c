import time

from BaseMonkey import BaseDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from util.logger import log_print
from models.monkey_context import MonkeyContext


class HuajiaoDialog(BaseDialog):
    def __init__(self, context: MonkeyContext):
        super().__init__(context)  # 调用父类的初始化方法

    # 关闭青少年弹窗
    def close_young_mode_dialog(self):
        young_mode_dialog = self.d(
            resourceId="com.huajiao:id/childmode_dialog_bg")
        if young_mode_dialog.exists:
            ok_btn = young_mode_dialog.child(
                resourceId="com.huajiao:id/childmode_dialog_ok")
            if ok_btn.exists:
                log_print("找到青少年弹窗，点击确定")
                ok_btn.click()
                time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    # 关闭应用列表权限弹窗
    def close_app_list_permission_dialog(self):
        app_list_permission_dialog = self.d.xpath(
            '//*[@text="为确保您设备操作环境安全，防止恶意程序和反作弊，花椒直播申请获取您设备上的软件列表信息。"]')
        if app_list_permission_dialog.exists:
            # 找到同层级的取消按钮
            cancel_btn = self.d(resourceId="com.huajiao:id/tv_cancel")
            if cancel_btn.exists:
                log_print("找到应用列表权限弹窗，点击取消")
                cancel_btn.click()
                time.sleep(self.action_normal_dur)
            else:
                log_print("未找到取消按钮")

            return True
        else:
            return False

    def close_web_dialog(self):
        web_dialog = self.d(resourceId="com.huajiao:id/webview_container")
        if web_dialog.exists:
            close_btn = self.d(resourceId="com.huajiao:id/close_btn")
            if close_btn.exists:
                log_print("找到web弹窗，点击关闭按钮")
                close_btn.click()
                time.sleep(self.action_normal_dur)
            else:
                log_print("未找到关闭按钮，尝试使用返回按钮")
                self.d.press("back")
                time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_web_dialog2(self):
        web_dialog = self.d(
            resourceId="com.huajiao:id/user_agreen_webview_container")
        if web_dialog.exists:
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_jump_other_room_dialog(self):
        dialog = self.d.xpath('//*[@text="是否跳转到该主播直播间?"]')
        if dialog.exists:
            cancel_btn = self.d(resourceId="com.huajiao:id/tv_cancel")
            if cancel_btn.exists:
                log_print("找到跳转其他房间弹窗，点击取消")
                cancel_btn.click()
                time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def close_account_security_dialog(self):
        """由于您已多次违规，现进行封禁处理"""
        account_security_dialog = self.d.xpath('//*[@text="由于您已多次违规，现进行封禁处理"]')
        if account_security_dialog.exists:
            log_print("找到多次违规，现进行封禁处理弹窗，点击关闭")
            # close_btn = self.d(resourceId="com.huajiao:id/close_iv")
            # close_btn.click_exists()
            time.sleep(self.action_normal_dur)
            raise TerminatedMonkeyError.create_terminated_app_run("账号异常")
        else:
            return False

    def close_login_dialog(self):
        login_dialog = self.d.xpath('//*[@text="手机验证码登录"]')
        if login_dialog.exists:
            log_print("找到手机验证码登录弹窗")
            time.sleep(self.action_normal_dur)
            raise TerminatedMonkeyError.create_terminated_app_run(
                "账号异常-手机验证码登录")
        else:
            return False

    def try_dismiss(self):
        result = False
        if self.close_young_mode_dialog():
            result = True
        if self.close_app_list_permission_dialog():
            result = True
        if self.close_web_dialog():
            result = True
        if self.close_jump_other_room_dialog():
            result = True
        if self.close_web_dialog2():
            result = True
        if self.close_account_security_dialog():
            result = True

        return result
