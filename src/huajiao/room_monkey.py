import time
from BaseMonkey import ActivityMonkey
from huajiao.hua<PERSON>ao_dialog import HuajiaoDialog
from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from util.logger import log_print
from models.monkey_context import MonkeyContext


class RoomMonkey(ActivityMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context, app_config, "com.huajiao/.detail.WatchesListActivity")
        self.text = app_config.text
        self.room_count = app_config.count
        self._total_process = app_config.count
        self.dialog_monkey = HuajiaoDialog(context)

    def run(self):
        try:
            self.run_room()
        except TerminatedMonkeyError as e:
            if e.code == TerminatedMonkeyError.CODE_PROCESS_FINISHED:
                log_print(f"RoomMonkey 任务完成，中断直播间任务")
                return True
            else:
                raise e

    def run_room(self):
        d = self.d
        time.sleep(self.action_normal_dur)
        fail = 0
        self._fail_reason_dict = {}
        while self.get_process() < self.get_total_process():
            self.dialog_monkey.try_dismiss()
            send_result = False
            fail_reason = ""
            privately_send_result, privately_fail_reason = self.chat_privately()
            publicly_send_result, publicly_fail_reason = self.chat_publicly()
            if privately_send_result:
                send_result = True
            if publicly_send_result:
                send_result = True
            if privately_fail_reason:
                fail_reason = privately_fail_reason
            if publicly_fail_reason:
                fail_reason = publicly_fail_reason
            if send_result:
                self._success_count += 1
            else:
                fail += 1
                # 统计失败原因
                if fail_reason:
                    if fail_reason in self._fail_reason_dict:
                        self._fail_reason_dict[fail_reason] += 1
                    else:
                        self._fail_reason_dict[fail_reason] = 1
            log_print(
                f"发送结果 {send_result}: 成功: {self._success_count}, 失败: {fail}, 失败原因: {fail_reason}")
            log_print(
                f"私聊结果 {privately_send_result}: 成功: {self._success_count}, 失败: {fail}, 失败原因: {privately_fail_reason}")
            log_print(
                f"公聊结果 {publicly_send_result}: 成功: {self._success_count}, 失败: {fail}, 失败原因: {publicly_fail_reason}")
            self.do_process(self.get_process() + 1)
            time.sleep(self.action_short_dur)
            self.dialog_monkey.try_dismiss()
            # 私聊每天10次限制
            if self._success_count >= 10:
                raise TerminatedMonkeyError.create_process_finished()
            # 7. 向上滑动切换下一个主播
            width, height = d.window_size()
            log_print(
                f"向上滑动切换下一个主播: {self.get_process() + 1}/{self.room_count}")
            d.swipe(width * 0.5, height * 0.4, width * 0.5, height * 0.2, 0.1)
            time.sleep(self.action_long_dur)
        # 新增：打印所有失败原因统计
        if self._fail_reason_dict:
            log_print("\n失败原因统计：")
            for reason, count in self._fail_reason_dict.items():
                log_print(f"{reason}: {count} 次")
        else:
            log_print("无失败原因，全部成功！")

    def chat_privately(self):
        d = self.d
        send_result = False
        fail_reason = ""
        # 1. 点击主播头像
        avatar = d(resourceId="com.huajiao:id/header_civ")
        if self.retry_exists(avatar, "主播头像"):
            log_print("找到主播头像，点击主播头像")
            self.safe_click(avatar, "avatar")
            time.sleep(self.action_short_dur)
            # 2. 点击私信按钮
            msg_btn = d(resourceId="com.huajiao:id/layout_message")
            if self.retry_exists(msg_btn, "私信按钮"):
                log_print("找到私信按钮，点击私信按钮")
                self.safe_click(msg_btn)
                time.sleep(self.action_normal_dur)
                # 3. 检查是否已发过相同文案
                chat_list = d(
                    resourceId="com.huajiao:id/chatactivity_listview")
                has_sent = False
                if self.retry_exists(chat_list, "聊天列表"):
                    idx = 0
                    while True:
                        item = chat_list.child(index=idx)
                        if not item.exists:
                            break
                        textview = item.child(
                            resourceId="com.huajiao:id/chat_right_text_textview_chattextcontent")
                        if textview.exists and textview.get_text() == self.text:
                            log_print("找到相同文案")
                            has_sent = True
                            break
                        idx += 1
                if not has_sent:
                    # 4. 输入文案并发送
                    input_box = d(
                        resourceId="com.huajiao:id/chatactivity_edittext_input")
                    time.sleep(self.action_short_dur)
                    send_btn = d(
                        resourceId="com.huajiao:id/chatactivity_button_send")
                    if self.retry_exists(input_box, "输入框"):
                        log_print("找到输入框和发送按钮，输入文案")
                        self.safe_set_text(input_box, self.text, "私聊输入文案")
                        time.sleep(self.action_short_dur)
                        if self.retry_exists(send_btn, "发送按钮"):
                            log_print("找到发送按钮，点击发送按钮")
                            self.safe_click(send_btn)
                            time.sleep(self.action_normal_dur)
                            if self.retry_exists(chat_list, "聊天列表"):
                                log_print("点击聊天列表,触发键盘收起")
                                self.safe_click(chat_list)
                                time.sleep(self.action_normal_dur)
                                check_send_msg_index = 0
                                while True:
                                    item = chat_list.child(
                                        index=check_send_msg_index)
                                    if not item.exists:
                                        fail_reason = "发送失败，未在聊天列表中找到发送的文案"
                                        break
                                    textview = item.child(
                                        resourceId="com.huajiao:id/chat_right_text_textview_chattextcontent")
                                    if textview.exists and textview.get_text() == self.text:
                                        fail_icon = item.child(
                                            resourceId="com.huajiao:id/chat_right_text_imageview_sendfail")
                                        if fail_icon.exists:
                                            log_print("找到发送的文案失败图标，发送失败")
                                            send_result = False
                                            fail_reason = "发送失败，找到发送文案失败图标"
                                        else:
                                            log_print("找到发送的文案，发送成功")
                                            send_result = True
                                        break
                                    check_send_msg_index += 1

                        else:
                            log_print("未找到发送按钮")
                            fail_reason = "未找到发送按钮"
                            time.sleep(self.action_short_dur)
                    else:
                        log_print("未找到输入框和发送按钮")
                        fail_reason = "未找到输入框和发送按钮"
                        time.sleep(self.action_short_dur)
                else:
                    fail_reason = "已经发送过文案"
                # 5. 关闭聊天窗口
                close_btn = d(
                    resourceId="com.huajiao:id/chatactivity_imagebutton_left_return")
                if self.retry_exists(close_btn, "关闭聊天窗口按钮"):
                    log_print("找到关闭聊天窗口按钮，点击关闭聊天窗口按钮")
                    self.safe_click(close_btn)
                    time.sleep(self.action_normal_dur)

                # 6. 返回关闭主播卡片
                log_print("返回关闭主播卡片")
                d.press("back")
            else:
                fail_reason = "未找到私聊按钮"
                log_print("未找到私聊按钮")
        else:
            fail_reason = "未找到主播头像"
            log_print("未找到主播头像")

        return send_result, fail_reason

    # 公屏聊天
    def chat_publicly(self) -> tuple[bool, str]:
        d = self.d
        send_result = False
        fail_reason = ""

        if not self.app_config.text_publicly:
            return send_result, fail_reason

        # 1. 点击公屏按钮
        public_btn = d(resourceId="com.huajiao:id/btn_comment")
        if self.retry_exists(public_btn, "公屏按钮"):
            log_print("找到公屏按钮，点击公屏按钮")
            self.safe_click(public_btn)
            time.sleep(self.action_normal_dur)

            # 2. 输入文案并发送
            input_box = d(resourceId="com.huajiao:id/edit")
            if self.retry_exists(input_box, "公屏输入框"):
                log_print("找到公屏输入框，输入文案")
                input_box.set_text(self.app_config.text_publicly)
                time.sleep(self.action_short_dur)

                # 3. 点击发送按钮
                send_btn = d(resourceId="com.huajiao:id/btn_send")
                if self.retry_exists(send_btn, "公屏发送按钮"):
                    log_print("找到公屏发送按钮，点击发送按钮")
                    self.safe_click(send_btn)
                    send_result = True
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到发送按钮")
                    fail_reason = "未找到发送按钮"
                    time.sleep(self.action_short_dur)
            else:
                log_print("未找到输入框")
                fail_reason = "未找到输入框"

            # 点击屏幕其他区域，收起键盘
            # d.hide_keyboard() # 会让输入键盘变成ADBKeyboard，然后顶得很高
            try:
                d.click(0.3, 0.3)
            except Exception as e:
                log_print(f"点击屏幕其他区域(头像)，收起键盘失败: {e}")
            print("点击屏幕其他区域(头像)，收起键盘")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到公屏按钮")
            fail_reason = "未找到公屏按钮"

        return send_result, fail_reason
