# -*- coding: utf-8 -*-
import os
import time
from abc import ABC, abstractmethod
from typing import Callable, Dict, List, Tuple

from uiautomator2.xpath import XPathSelector

from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from models.monkey_context import Monkey<PERSON>ontext
from uiautomator2 import UiObject, Device
from util.adb_util import screenshot
from util.logger import log_print, set_log_context

ProcessCallback = Callable[[int, int], None]


class BaseMonkey:
    def __init__(self, context: MonkeyContext):
        self.context = context
        self.d: Device = context.get_ui_automator_device()
        self.action_short_dur = 1.5
        self.action_normal_dur = 3
        self.action_long_dur = 6
        self._total_process = 0
        self._process = 0
        self._success_count = 0
        self._progress_callbacks: List[ProcessCallback] = []
        self._fail_reason_dict: Dict[str, int] = {}
        self.__hit_fail_action: <PERSON><PERSON>[str, int] or None = None  # 命中失败行为
        self.retry_action: Callable[[str, int], None] = lambda tag, i: None

        # 设置日志上下文（如果有设备序列号）
        if hasattr(self.d, 'serial'):
            set_log_context(device_serial=self.d.serial)

    def run(self) -> bool:
        return False

    def do_process(self, process: int):
        self._process = process
        for cb in self._progress_callbacks:
            cb(self._process, self._total_process)

    def register_progress_callback(self, callback: ProcessCallback):
        """注册进度回调，callback 需为 (process, total_process) -> None 的函数"""
        if callback not in self._progress_callbacks:
            self._progress_callbacks.append(callback)

    def unregister_progress_callback(self, callback: ProcessCallback):
        """注销进度回调"""
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)

    def retry_until_true(self, condition_func, action_func=None, tag: str = None, retry_count=1):
        for i in range(retry_count + 1):
            if condition_func():
                self.eliminate_fail_action()
                return True
            if action_func is not None:
                action_func(tag, i)
            self.hit_fail_action("Unknown" if tag is None else tag)
        return False

    def retry_when_false(self, condition_func, tag: str = None, retry_count=1):
        return self.retry_until_true(condition_func, self.retry_action, tag, retry_count)

    def get_fail_reason_dict(self):
        return self._fail_reason_dict

    def get_process(self):
        return self._process

    def get_total_process(self):
        return self._total_process

    def get_success_count(self):
        return self._success_count

    def eliminate_fail_action(self):
        self.__hit_fail_action = None

    def hit_fail_action(self, fail_reason: str):
        if self.__hit_fail_action is None:
            self.__hit_fail_action = (fail_reason, 1)
            self.on_hit_fail_action(fail_reason, 1)
        else:
            self.__hit_fail_action = (
                self.__hit_fail_action[0], self.__hit_fail_action[1] + 1)
            self.on_hit_fail_action(fail_reason, self.__hit_fail_action[1])

    def on_hit_fail_action(self, fail_reason: str, count: int):
        if count > 14:
            error_msg = f"多次连续命中失败原因{fail_reason}，终止任务"
            log_print(f"{error_msg}")
            raise TerminatedMonkeyError.create_terminated_app_run(error_msg)

    def get_hit_fail_action(self):
        return self.__hit_fail_action

    def safe_click(self, element: UiObject or XPathSelector, tag: str = None) -> bool:
        try:
            element.click()
            return True
        except Exception as e:
            log_print(f"点击元素f{tag} 失败: {e}")
            self.retry_action(tag, 0)
            try:
                element.click_exists()
                return True
            except Exception as e:
                log_print(f"再次点击元素f{tag} 失败: {e}")
                return False

    def safe_set_text(self, element: UiObject, text: str, tag: str = None) -> bool:
        try:
            element.set_text(text)
            return True
        except Exception as e:
            log_print(f"输入文本{text}到元素f{tag} 失败: {e}")
            self.retry_action(tag, 0)
            try:
                element.set_text(text)
                return True
            except Exception as e:
                log_print(f"再次输入文本{text}到元素f{tag} 失败: {e}")
                return False

    def retry_exists(self, element: UiObject or XPathSelector, tag: str = None, retry_count=16) -> bool:
        return self.retry_when_false(lambda: element.exists, tag, retry_count)


class BaseAuto(BaseMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context)
        self.app_config = app_config
        # 设置应用名称到日志上下文
        set_log_context(app_name=app_config.name)

    def screenshot(self):
        try:
            os.makedirs(f"{self.context.get_result_dir_path()}", exist_ok=True)
            file_path = f"{self.context.get_result_dir_path()}/snapshot_{self.app_config.name}_{int(time.time())}.png"
            screenshot(file_path, self.d.serial)
        except Exception as e:
            log_print(f"截图失败: {e}")


class BaseDialog(BaseMonkey):
    def __init__(self, context: MonkeyContext):
        super().__init__(context)

    def try_dismiss(self):
        return False


class AppMonkey(BaseAuto, ABC):

    @abstractmethod
    def get_app_package_name(self) -> str:
        """获取app包名"""
        pass

    def run_app(self) -> bool:
        self.d.app_stop(self.get_app_package_name())
        time.sleep(self.action_normal_dur)
        self.d.app_start(self.get_app_package_name())
        time.sleep(self.action_normal_dur)
        result = self.run()
        time.sleep(self.action_normal_dur)
        self.d.app_stop(self.get_app_package_name())
        return result


class ActivityMonkey(BaseAuto):

    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig, activity: str):
        super().__init__(context, app_config)
        self.activity = activity
        self.dialog_monkey = BaseDialog(context)
        self.retry_action = lambda tag, i: (
            not self.dialog_monkey.try_dismiss())
        self._total_process = app_config.count

    def check_activity_and_back(self):
        info = self.d.app_current()
        activity = info["activity"]
        if activity == self.activity:
            self.eliminate_fail_action()
            return
        else:
            self.d.press("back")
            error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_long_dur}秒"
            log_print(f"{error_msg}")
            self.hit_fail_action(error_msg)
            time.sleep(self.action_long_dur)
            self.check_activity_and_back()
