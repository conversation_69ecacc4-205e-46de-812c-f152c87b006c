import os
import time
from abc import ABC, abstractmethod
from typing import Callable, Dict, List, Tuple

from uiautomator2.xpath import XPathSelector

from models.terminated_monkey_error import TerminatedMonkeyError
from monkey_config import AppMonkeyConfig
from models.monkey_context import MonkeyContext
from uiautomator2 import UiObject, Device
from util.adb_util import screenshot
from util.logger import log_print, set_log_context
from typing import Union

ProcessCallback = Callable[[int, int], None]

DEFAULT_CHECK_EXISTS_COUNT = 8  # 默认检测控件是否存在，要比命中失败终止的次数大
DEFAULT_TERMINATE_ON_HIT_FAIL_COUNT = 7  # 默认命中失败次数时终止


class BaseMonkey:
    def __init__(self, context: MonkeyContext):
        self.context = context
        self.d: Device = context.get_ui_automator_device()
        self.action_short_dur = 1.5
        self.action_normal_dur = 2.5
        self.action_long_dur = 5
        self._total_process = 0
        self._process = 0
        self._success_count = 0
        self._progress_callbacks: List[ProcessCallback] = []
        self._fail_reason_dict: Dict[str, int] = {}
        # 命中失败行为，失败热度膨胀缓存
        self.__hit_fail_action: Union[Tuple[str, int], None] = None
        self.retry_action: Callable[[str, int], None] = lambda tag, i: None

        # 设置日志上下文（如果有设备序列号）
        if hasattr(self.d, 'serial'):
            set_log_context(device_serial=self.d.serial)

    def run(self) -> bool:
        return False

    def do_process(self, process: int):
        self._process = process
        for cb in self._progress_callbacks:
            cb(self._process, self._total_process)

    def register_progress_callback(self, callback: ProcessCallback):
        """注册进度回调，callback 需为 (process, total_process) -> None 的函数"""
        if callback not in self._progress_callbacks:
            self._progress_callbacks.append(callback)

    def unregister_progress_callback(self, callback: ProcessCallback):
        """注销进度回调"""
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)

    def retry_until_true(self, condition_func: Callable[[], Union[bool, Tuple[bool, str]]],
                         action_func: Callable[[str, int], None] = None,
                         tag: str = None, retry_count=1, expand_fail: bool = True):
        """
        重试condition_func直到返回True
        :param condition_func 目标函数判断
        :param action_func 重试前调用的函数
        :param tag 标签，记录错误tag
        :param retry_count 重试次数
        :param expand_fail 是否膨胀错误
        """
        for i in range(retry_count + 1):
            result = condition_func()
            result_str = None
            if isinstance(result, tuple):
                result_bool = result[0]
                result_str = result[1]
            else:
                result_bool = result
            if tag and result_str:
                result_tag = f"{tag}::{result_str}"
            elif tag:
                result_tag = tag
            else:
                result_tag = result_str
            if result_bool:
                if expand_fail:
                    self.eliminate_fail_action()
                return True
            if action_func is not None:
                action_func(result_tag, i)
            if expand_fail:
                self.hit_fail_action(
                    "Unknown" if result_tag is None else result_tag)
        return False

    def retry_when_false(self, condition_func, tag: str = None, retry_count=1):
        return self.retry_until_true(condition_func, self.retry_action, tag, retry_count)

    def get_fail_reason_dict(self):
        return self._fail_reason_dict

    def get_process(self):
        return self._process

    def get_total_process(self):
        return self._total_process

    def get_success_count(self):
        return self._success_count

    def eliminate_fail_action(self):
        """
        剔除失败行为，重置失败热点
        """
        self.__hit_fail_action = None

    def hit_fail_action(self, fail_reason: str):
        """
        命中失败行为，膨胀失败热点
        :param fail_reason 失败原因
        """
        if self.__hit_fail_action is None:
            self.__hit_fail_action = (fail_reason, 1)
            self.on_hit_fail_action(fail_reason, 1)
        else:
            self.__hit_fail_action = (
                self.__hit_fail_action[0], self.__hit_fail_action[1] + 1)
            self.on_hit_fail_action(fail_reason, self.__hit_fail_action[1])

    def on_hit_fail_action(self, fail_reason: str, count: int):
        log_print(f"失败行为膨胀 原因: {fail_reason}，失败次数：{count}")
        if count > DEFAULT_TERMINATE_ON_HIT_FAIL_COUNT:
            error_msg = f"多次连续命中失败原因{fail_reason}，终止任务"
            log_print(f"{error_msg}")
            raise TerminatedMonkeyError.create_terminated_app_run(error_msg)

    def get_hit_fail_action(self):
        return self.__hit_fail_action

    def safe_click(self, element: Union[UiObject, XPathSelector], tag: str = None) -> bool:
        try:
            element.click()
            return True
        except Exception as e:
            log_print(f"点击元素f{tag} 失败: {e}")
            self.retry_action(tag, 0)
            try:
                element.click_exists()
                return True
            except Exception as e:
                log_print(f"再次点击元素f{tag} 失败: {e}")
                return False

    def safe_set_text(self, element: UiObject, text: str, tag: str = None) -> bool:
        try:
            element.set_text(text)
            return True
        except Exception as e:
            log_print(f"输入文本{text}到元素f{tag} 失败: {e}")
            self.retry_action(tag, 0)
            try:
                element.set_text(text)
                return True
            except Exception as e:
                log_print(f"再次输入文本{text}到元素f{tag} 失败: {e}")
                return False

    def retry_exists(self, element: Union[UiObject, XPathSelector], tag: str = None,
                     retry_count=DEFAULT_CHECK_EXISTS_COUNT) -> bool:
        return self.retry_when_false(lambda: element.exists, tag, retry_count)


class BaseAuto(BaseMonkey):
    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig):
        super().__init__(context)
        self.app_config = app_config
        # 设置应用名称到日志上下文
        set_log_context(app_name=app_config.name)

    def screenshot(self, file_name: str = None):
        try:
            os.makedirs(f"{self.context.get_result_dir_path()}", exist_ok=True)
            snapshot_name = file_name or f"snapshot_{self.app_config.name}_{int(time.time())}.png"
            file_path = f"{self.context.get_result_dir_path()}/{snapshot_name}"
            # file_path = f"{self.context.get_result_dir_path()}/{file_name or f"snapshot_{self.app_config.name}_{int(time.time())}.png"}"
            screenshot(file_path, self.d.serial)
        except Exception as e:
            log_print(f"截图失败: {e}")


class BaseDialog(BaseMonkey):
    def __init__(self, context: MonkeyContext):
        super().__init__(context)

    def try_dismiss(self):
        return False


class AppMonkey(BaseAuto, ABC):
    version: str = "Unknown"

    @abstractmethod
    def get_app_package_name(self) -> str:
        """获取app包名"""
        pass

    def run_app(self) -> bool:
        self.d.app_stop(self.get_app_package_name())
        time.sleep(self.action_normal_dur)
        try:
            device = self.context.get_ui_automator_device()
            output = device.shell(f"dumpsys package {self.get_app_package_name()} | grep versionName").output
            self.version = output.strip().split("=")[-1] if output else "Unknown"
        except Exception as e:
            log_print("获取版本失败")
        log_print(f"开始运行 {self.get_app_package_name()} 版本号：{self.version}")
        self.d.app_start(self.get_app_package_name())
        time.sleep(self.action_normal_dur)
        result = self.run()
        time.sleep(self.action_normal_dur)
        self.d.app_stop(self.get_app_package_name())
        return result


class ActivityMonkey(BaseAuto):

    def __init__(self, context: MonkeyContext, app_config: AppMonkeyConfig, activity: str):
        super().__init__(context, app_config)
        self.activity = activity
        self.dialog_monkey = BaseDialog(context)
        self.retry_action = lambda tag, i: (
            not self.dialog_monkey.try_dismiss())
        self._total_process = app_config.count

    def check_activity_and_back(self, retry_count: int = 5, expand_fail: bool = True):
        """
        检查当前activity，不符合就回退
        :param expand_fail  是否膨胀失败行为热点
        :param retry_count 重试次数
        """

        def _check_activity_and_back() -> Tuple[bool, str]:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.activity:
                return True, f"检查Activity，当前{activity}符合条件"
            else:
                self.d.press("back")
                error_msg = f"当前activity: {activity}，不是{self.activity}，点击返回按钮，等待{self.action_long_dur}秒"
                log_print(f"{error_msg}")
                time.sleep(self.action_long_dur)
                return False, f"{error_msg}"

        self.retry_until_true(_check_activity_and_back,
                              None, None, retry_count, expand_fail)
