from typing import List

from BaseMonkey import BaseMonkey


class MonkeyProxyMixin:
    proxy_monkey: BaseMonkey
    proxy_hit_fail_action_monkeys: List[BaseMonkey] = []

    def register_progress_callback(self, callback):
        return self.proxy_monkey.register_progress_callback(callback)

    def unregister_progress_callback(self, callback):
        return self.proxy_monkey.unregister_progress_callback(callback)

    def get_fail_reason_dict(self):
        return self.proxy_monkey.get_fail_reason_dict()

    def get_process(self):
        return self.proxy_monkey.get_process()

    def get_total_process(self):
        return self.proxy_monkey.get_total_process()

    def get_success_count(self):
        return self.proxy_monkey.get_success_count()

    def eliminate_fail_action(self):
        for proxy_hit_fail_action_monkey in self.proxy_hit_fail_action_monkeys:
            proxy_hit_fail_action_monkey.eliminate_fail_action()
