import time
from BaseMonkey import BaseAuto
from util.logger import log_print
from xinyu.xinyu_home import <PERSON>nyuHome
from xinyu.xinyu_room import XinyuRoom

# 心遇 App 自动灌水脚本


class XinyuAuto(BaseAuto):
    def __init__(self, context, app_config):
        super().__init__(context, app_config)
        self.room_monkey = XinyuRoom(context, app_config, None, -1)
        self.home_monkey = XinyuHome(context, app_config)

    def run(self):
        self.d.app_stop("com.netease.moyi")
        time.sleep(self.action_normal_dur)
        self.d.app_start("com.netease.moyi")
        while True:
            info = self.d.app_current()
            activity = info["activity"]
            if activity == self.home_monkey.activity:
                self.home_monkey.run()
                break
            if activity == self.room_monkey.activity:
                self.room_monkey.run()
                break
            else:
                log_print(f"等待陌陌对应的Activity，当前Activity: {activity}")
                time.sleep(self.action_normal_dur)
