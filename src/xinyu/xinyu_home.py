import time
from BaseMonkey import ActivityMonkey
from util.logger import log_print
from xinyu.xinyu_dialog import XinyuDialog
from xinyu.xinyu_room import XinyuRoom


class XinyuHome(ActivityMonkey):
    def __init__(self, context, app_config):
        super().__init__(context, app_config, ".biz.home.impl.NewMainActivity")
        self.momo_dialog = XinyuDialog(context)
        self.runRoomIndex = 0
        self.rvIndex = 0
        self.success = 0
        self.fail = 0
        self.fail_reason_dict = {}

    def run(self):
        return self.runLoop(0)

    def runLoop(self, deep: int):
        if deep > 3:
            log_print("循环次数超过3次，退出")
            return False
        deep += 1
        d = self.d
        self.momo_dialog.try_dismiss()
        # 切换到底部娱乐交友tab
        live_tab = d(
            resourceId="com.netease.moyi:id/biz_home_bottom_tab_party")
        if live_tab.exists:
            log_print("点击底部娱乐交友tab")
            live_tab.click()
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到娱乐交友tab，等待循环执行")
            time.sleep(self.action_normal_dur)
            return self.runLoop(deep)

        # 切换到娱乐子tab
        entertainment_tab = d.xpath('//*[@text="娱乐"]')
        if entertainment_tab.exists:
            log_print("点击娱乐子tab")
            entertainment_tab.click()
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到娱乐子tab，等待循环执行")
            time.sleep(self.action_normal_dur)
            return self.runLoop(deep)

        rv = d(resourceId="com.netease.moyi:id/recyclerView")
        if not rv.exists:
            log_print("未找到娱乐直播列表，等待循环执行")
            return self.runLoop(deep)

        self.rvIndex = 0

        self.process_room_list()

        if self.fail_reason_dict:
            log_print("\n失败原因统计：")
            for reason, count in self.fail_reason_dict.items():
                log_print(f"{reason}: {count} 次")
        else:
            log_print("无失败原因，全部成功！")

        return True

    def process_room_list(self):
        while True:
            if self.runRoomIndex >= self.app_config.count:
                log_print("已完成进入次数..")
                break
            rv = self.d(resourceId="com.netease.moyi:id/recyclerView")
            if not rv.exists:
                log_print("未找到娱乐直播列表 process_room_list 失败")
                break
            find_room_result = self.find_room()
            if not find_room_result:
                # 尝试向上滑动列表
                self.d.swipe(0.5, 0.8, 0.5, 0.2, 0.1)
                time.sleep(self.action_normal_dur)
                self.rvIndex = 0  # 是从可见区域开始的，要变回0
                continue

            time.sleep(self.action_normal_dur)
            self.rvIndex += 1

    def find_room(self) -> bool:
        rv = self.d(resourceId="com.netease.moyi:id/recyclerView")
        if not rv.exists:
            log_print("未找到娱乐直播列表 find_room 失败")
            return False

        # 查找子View，也就是RecyclerView的item
        item = rv.child(index=self.rvIndex)
        if item.exists:
            log_print(f"找到第{self.rvIndex + 1}个item")
            anchor_name_view = item.child(
                resourceId="com.netease.moyi:id/titleView")
            if anchor_name_view.exists:
                anchor_name = anchor_name_view.get_text()
                log_print(f"点击第{self.rvIndex + 1}个item，主播名字: {anchor_name}")
                # item.click()
                anchor_name_view.click()
                time.sleep(self.action_long_dur)
                room_monkey = XinyuRoom(
                    self.d, self.app_config, anchor_name, self.rvIndex)
                room_monkey.run()

                send_result = False
                fail_reason = ""

                if room_monkey.send_publicly_result:
                    send_result = True

                if room_monkey.send_publicly_fail_reason:
                    fail_reason = room_monkey.send_publicly_fail_reason

                if room_monkey.send_privately_result:
                    send_result = True

                if room_monkey.send_privately_fail_reason:
                    fail_reason = room_monkey.send_privately_fail_reason

                if send_result:
                    self.success += 1
                    self.runRoomIndex += 1
                else:
                    self.fail += 1
                    if fail_reason:
                        if fail_reason in self.fail_reason_dict:
                            self.fail_reason_dict[fail_reason] += 1
                        else:
                            self.fail_reason_dict[fail_reason] = 1
                log_print(
                    f"发送结果 {send_result}: 成功: {self.success}, 失败: {self.fail}, 失败原因: {fail_reason}")
                time.sleep(self.action_normal_dur)
                return True
            else:
                log_print("未找到主播名字 find_room 失败")
                return False
        else:
            log_print(f"未找到第{self.rvIndex + 1}个item，等待循环执行")
            return False
