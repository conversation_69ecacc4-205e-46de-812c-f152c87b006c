import time
from BaseMonkey import ActivityMonkey
from monkey_config import AppMonkeyConfig
from util.logger import log_print
from xinyu.xinyu_dialog import XinyuDialog


class XinyuRoom(ActivityMonkey):
    def __init__(self, context, app_config: AppMonkeyConfig, anchor_name: str, listIndex: int):
        super().__init__(context, app_config, ".biz.partyhouse.impl.live.PartyLiveActivity")
        self.text = app_config.text
        self.room_count = app_config.count
        self.dialog_monkey = XinyuDialog(context)
        self.anchor_name = anchor_name
        self.listIndex = listIndex
        self.action_short_dur = 2
        self.action_normal_dur = 4
        self.action_long_dur = 8

    def run(self):
        d = self.d
        time.sleep(self.action_normal_dur)
        self.dialog_monkey.try_dismiss()

        time.sleep(self.action_normal_dur)

        # 检测现在的直播间主播名字
        anchor_name_view = d(resourceId="com.netease.moyi:id/roomTitle")

        send_publicly_result, send_publicly_fail_reason = self.chat_publicly()
        send_privately_result, send_privately_fail_reason = self.chat_privately()

        self.send_publicly_result = send_publicly_result
        self.send_publicly_fail_reason = send_publicly_fail_reason
        self.send_privately_result = send_privately_result
        self.send_privately_fail_reason = send_privately_fail_reason

        log_print(
            f"公屏发送结果 {send_publicly_result} 失败原因: {send_publicly_fail_reason}")
        log_print(
            f"私信发送结果 {send_privately_result} 失败原因: {send_privately_fail_reason}")
        time.sleep(self.action_short_dur)
        # 退出直播间
        log_print("按back退出直播间")
        d.press("back")
        time.sleep(self.action_short_dur)

    # 公屏聊天

    def chat_publicly(self) -> tuple[bool, str]:
        d = self.d
        send_result = False
        fail_reason = ""

        # 1. 点击公屏按钮
        public_btn = d(resourceId="com.netease.moyi:id/commentButton")
        if public_btn.exists:
            log_print("找到公屏按钮，点击公屏按钮")
            public_btn.click()
            time.sleep(self.action_normal_dur)

            # 2. 输入文案并发送
            input_box = d(resourceId="com.netease.moyi:id/commentEditText")
            if input_box.exists:
                log_print("找到输入框，输入文案")
                input_box.set_text(self.app_config.text_publicly)
                time.sleep(self.action_short_dur)

                # 3. 点击发送按钮
                send_btn = d(resourceId="com.netease.moyi:id/sendButton")
                if send_btn.exists:
                    log_print("找到公屏发送按钮，点击发送按钮")
                    send_btn.click()
                    send_result = True
                    time.sleep(self.action_normal_dur)
                    self.dialog_monkey.try_dismiss()
                else:
                    log_print("未找到发送按钮")
                    fail_reason = "未找到发送按钮"
                    time.sleep(self.action_short_dur)
            else:
                log_print("未找到输入框")
                fail_reason = "未找到输入框"

            # d.click(0.3, 0.3)
            d.press("back")
            print("点击屏幕其他区域(头像)，收起键盘")
            time.sleep(self.action_normal_dur)
        else:
            log_print("未找到公屏按钮")
            fail_reason = "未找到公屏按钮"

        return send_result, fail_reason

    # 私信聊天
    def chat_privately(self) -> tuple[bool, str]:
        d = self.d
        send_result = False
        fail_reason = ""

        # 1. 点击主播头像
        avatar = d(resourceId="com.netease.moyi:id/ownerAvatar")
        if avatar.exists:
            log_print("找到主播头像，点击主播头像")
            avatar.click()
            time.sleep(5)

            # 2. 点击私信按钮
            msg_btn = d(resourceId="com.netease.moyi:id/chat_user_layout")
            if msg_btn.exists:
                log_print("找到私信按钮，点击私信按钮")
                msg_btn.click()
                time.sleep(self.action_normal_dur)

                # 3. 检查是否已发过相同文案
                chat_list = d(
                    resourceId="com.netease.moyi:id/biz_msg_chat_message_list_view")
                has_sent = False
                if chat_list.exists:
                    idx = 0
                    while True:
                        item = chat_list.child(index=idx)
                        if not item.exists:
                            break
                        textview = item.child(
                            resourceId="com.netease.moyi:id/nim_message_item_text_body")
                        if textview.exists and textview.get_text() == self.text:
                            log_print("找到相同文案，已发送过")
                            has_sent = True
                            fail_reason = "已经发送过文案"
                            break
                        idx += 1

                if not has_sent:
                    # 4. 输入文案并发送
                    input_box = d(
                        resourceId="com.netease.moyi:id/editTextView")

                    if input_box.exists:
                        log_print("找到输入框和发送按钮，输入文案")
                        input_box.set_text(self.text)
                        time.sleep(self.action_short_dur)
                        send_btn = d(resourceId="com.netease.moyi:id/sendView")
                        if send_btn.exists:
                            log_print("点击发送按钮发送私聊")
                            send_btn.click()
                            time.sleep(self.action_normal_dur)
                            self.dialog_monkey.try_dismiss()
                        else:
                            log_print("未找到发送按钮")
                            fail_reason = "未找到发送按钮"

                        # 5. 点击其他区域移除焦点，收起键盘
                        log_print("私聊点击其他区域移除焦点，收起键盘")
                        # d.click(0.1, 0.4)
                        d.press("back")
                        time.sleep(self.action_normal_dur)
                        self.dialog_monkey.try_dismiss()

                        # 6. 检测是否发送成功
                        if chat_list.exists:
                            check_send_msg_index = 0
                            while True:
                                item = chat_list.child(
                                    index=check_send_msg_index)
                                if not item.exists:
                                    fail_reason = "发送失败，未在聊天列表中找到发送的文案"
                                    break
                                textview = item.child(
                                    resourceId="com.netease.moyi:id/nim_message_item_text_body")
                                if textview.exists and textview.get_text() == self.text:
                                    log_print("找到发送的文案，发送成功")
                                    send_result = True
                                    break
                                check_send_msg_index += 1
                    else:
                        log_print("未找到输入框或发送按钮")
                        fail_reason = "未找到输入框或发送按钮"
                        time.sleep(self.action_short_dur)

                # 7. 关闭聊天窗口
                close_btn = d(resourceId="com.netease.moyi:id/backView")
                if close_btn.exists:
                    log_print("找到关闭聊天窗口按钮，点击关闭聊天窗口按钮")
                    close_btn.click()
                    time.sleep(self.action_normal_dur)
                else:
                    log_print("未找到关闭聊天窗口按钮，尝试使用回退按钮关闭")
                    self.d.press("back")
                    time.sleep(self.action_short_dur)
            else:
                log_print("未找到私信按钮")
                fail_reason = "未找到私信按钮"
                time.sleep(self.action_short_dur)

            # 8. 返回关闭主播卡片
            log_print("返回关闭主播卡片")
            d.press("back")
            time.sleep(self.action_short_dur)
        else:
            log_print("未找到主播头像")
            fail_reason = "未找到主播头像"
            time.sleep(self.action_short_dur)

        return send_result, fail_reason

        width, height = self.d.window_size()
        max_attempts = 3
        for attempt in range(max_attempts):
            # 每次滑动参数有微调
            x = width * (0.8 + 0.01 * attempt)
            x_end = width * (0.8 + 0.02 * attempt)
            y_start = height * (0.9 - 0.02 * attempt)
            y_end = height * (0.2 + 0.02 * attempt)
            # 不知道为什么水平滑一下后再触发第三次滑动，即再次垂直滑动时可以成功滑动
            if (attempt == 1):
                x = width * (0.2 + 0.01 * attempt)
                x_end = width * (0.8 + 0.01 * attempt)
                y_start = height * (0.4 - 0.02 * attempt)
                y_end = height * (0.4 + 0.02 * attempt)
            log_print(
                f"向上滑动切换下一个主播: {index + 1}/{self.room_count} attempt:{attempt+1} from:{x},{y_start} to:{x_end},{y_end}")
            self.d.swipe(x, y_start, x_end, y_end, 0.1)
            time.sleep(self.action_normal_dur)
            # 检查主播昵称
            anchor_name_view = self.d(resourceId="com.kugou.fanxing:id/gka")
            anchor_nanme = None
            if anchor_name_view.exists:
                try:
                    anchor_nanme = anchor_name_view.get_text()
                except Exception:
                    anchor_nanme = None
                if anchor_nanme is not None and anchor_nanme != self.anchor_name:
                    log_print(f"主播昵称已变化，切换成功, 昵称为：{anchor_nanme}")
                    return
            log_print(f"主播昵称未变化，重试滑动，昵称为：{anchor_nanme}")
        log_print("多次滑动后主播昵称依然未变化，可能切换失败")
