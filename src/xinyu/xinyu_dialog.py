import time

from BaseMonkey import BaseDialog
from util.logger import log_print


class XinyuDialog(BaseDialog):

    # 关闭直播间未知弹窗
    def closeMicrophoneDialog(self):
        microphoneDialog = self.d(
            resourceId="com.netease.moyi:id/slideContainer")
        if microphoneDialog.exists:
            log_print("找到直播间邀请上麦弹窗，点击返回关闭")
            self.d.press("back")
            time.sleep(self.action_normal_dur)
            return True
        else:
            return False

    def closeRealNameDialog(self):
        realNameDialog = self.d.xpath('//*[@text="请前往实名认证"]')
        if realNameDialog.exists:
            log_print("找到实名认证弹窗，点击返回关闭")
            self.d(resourceId="com.netease.moyi:id/txtCancel").click_exists()
            time.sleep(self.action_normal_dur)
            return True

    def try_dismiss(self):
        result = False
        if self.closeMicrophoneDialog():
            result = True
        if self.closeRealNameDialog():
            result = True
        return result
